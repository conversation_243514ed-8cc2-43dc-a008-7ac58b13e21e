<?php

namespace EssentialBlocks\Pro\Blocks;

use EssentialBlocks\Core\Block;

class Loop<PERSON>uilder extends Block
{
    protected $is_pro           = true;
    protected $editor_styles    = 'essential-blocks-pro-editor-style';
    protected $frontend_scripts = ['essential-blocks-pro-loop-builder-frontend'];
    protected $frontend_styles  = [];
    protected static $default_attributes;

    public function __construct()
    {
        self::$default_attributes = [
            // Query attributes
            'queryType'         => 'posts',
            'postType'          => 'post',
            'postsPerPage'      => 6,
            'orderBy'           => 'date',
            'order'             => 'desc',
            'includeIds'        => '',
            'excludeIds'        => '',
            'taxonomyFilters'   => [],
            'metaQuery'         => [],
            'dateQuery'         => [],
            'authorIds'         => [],
            'stickyPosts'       => false,

            // Layout attributes
            'layoutMode'        => 'grid',
            'paginationType'    => 'none',
            'showPreviewRows'   => true,
            'previewRowCount'   => 3,

            // Responsive columns
            'columnsDesktop'    => 3,
            'columnsTab'        => 2,
            'columnsMobile'     => 1,

            // Grid gap
            'gridGapDesktop'    => 20,
            'gridGapTab'        => 20,
            'gridGapMobile'     => 20,
            'gridGapUnit'       => 'px',
        ];
    }

    /**
     * Unique name of the block.
     * @return string
     */
    public function get_name()
    {
        return 'pro-loop-builder';
    }

    /**
     * Register all other scripts
     * @return void
     */
    public function register_scripts()
    {
        wpdev_essential_blocks_pro()->assets->register(
            'loop-builder-frontend',
            $this->path() . '/frontend.js'
        );
    }

    public function get_default_attributes()
    {
        return self::$default_attributes;
    }

    /**
     * Block render callback.
     *
     * @param mixed $attributes
     * @param mixed $content
     * @param mixed $block
     * @return mixed
     */
    public function render_callback($attributes, $content, $block = null)
    {
        if (is_admin()) {
            return;
        }

        $attributes = wp_parse_args($attributes, $this->get_default_attributes());

        // Extract attributes
        $block_id = isset($attributes['blockId']) ? $attributes['blockId'] : '';
        $class_hook = isset($attributes['classHook']) ? $attributes['classHook'] : '';
        $query_data = isset($attributes['queryData']) ? $attributes['queryData'] : [];
        $is_loop_builder = isset($attributes['isLoopBuilder']) ? $attributes['isLoopBuilder'] : true;

        // Add context filter to provide Loop Builder context to child blocks
        $filter_block_context = function ($context) use ($block_id, $query_data, $is_loop_builder) {
            // Add Loop Builder context
            $context['essential-blocks/queryId'] = $block_id;
            $context['essential-blocks/isLoopBuilder'] = $is_loop_builder;
            $context['essential-blocks/query'] = $query_data;

            // Also provide standard context keys for compatibility
            $context['queryId'] = $block_id;
            $context['query'] = $query_data;

            return $context;
        };

        // Add the filter with high priority to ensure it's applied early
        add_filter('render_block_context', $filter_block_context, 1);

        // Start output buffering
        ob_start();

        ?>
        <div class="eb-parent-wrapper eb-parent-<?php echo esc_attr($block_id); ?> <?php echo esc_attr($class_hook); ?>">
            <div class="eb-loop-builder-wrapper <?php echo esc_attr($block_id); ?>">
                <div class="eb-loop-builder-container">
                    <div class="eb-loop-builder-content">
                        <?php
                        // Render inner blocks content with context
                        echo $this->render_inner_blocks_with_context($content, $block);
                        ?>
                    </div>
                </div>
            </div>
        </div>
        <?php

        // Remove the context filter
        remove_filter('render_block_context', $filter_block_context, 1);

        return ob_get_clean();
    }



    /**
     * Render inner blocks with proper context injection
     *
     * @param string $content The block content
     * @param object $block The block object
     * @return string Rendered content
     */
    private function render_inner_blocks_with_context($content, $block)
    {
        if (empty($content)) {
            return '';
        }

        // Parse blocks from content
        $blocks = parse_blocks($content);

        // Render each block
        $output = '';
        foreach ($blocks as $inner_block) {
            $output .= render_block($inner_block);
        }

        return $output;
    }
}
