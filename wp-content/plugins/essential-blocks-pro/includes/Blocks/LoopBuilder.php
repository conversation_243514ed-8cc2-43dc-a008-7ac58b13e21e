<?php

namespace EssentialBlocks\Pro\Blocks;

use EssentialBlocks\Core\Block;

class Loop<PERSON>uilder extends Block
{
    protected $is_pro           = true;
    protected $editor_styles    = 'essential-blocks-pro-editor-style';
    protected $frontend_scripts = ['essential-blocks-pro-loop-builder-frontend'];
    protected $frontend_styles  = [];
    protected static $default_attributes;

    public function __construct()
    {
        self::$default_attributes = [
            // Query attributes
            'queryType'         => 'posts',
            'postType'          => 'post',
            'postsPerPage'      => 6,
            'orderBy'           => 'date',
            'order'             => 'desc',
            'includeIds'        => '',
            'excludeIds'        => '',
            'taxonomyFilters'   => [],
            'metaQuery'         => [],
            'dateQuery'         => [],
            'authorIds'         => [],
            'stickyPosts'       => false,

            // Layout attributes
            'layoutMode'        => 'grid',
            'paginationType'    => 'none',
            'showPreviewRows'   => true,
            'previewRowCount'   => 3,

            // Responsive columns
            'columnsDesktop'    => 3,
            'columnsTab'        => 2,
            'columnsMobile'     => 1,

            // Grid gap
            'gridGapDesktop'    => 20,
            'gridGapTab'        => 20,
            'gridGapMobile'     => 20,
            'gridGapUnit'       => 'px',
        ];
    }

    /**
     * Unique name of the block.
     * @return string
     */
    public function get_name()
    {
        return 'pro-loop-builder';
    }

    /**
     * Register all other scripts
     * @return void
     */
    public function register_scripts()
    {
        wpdev_essential_blocks_pro()->assets->register(
            'loop-builder-frontend',
            $this->path() . '/frontend.js'
        );
    }

    public function get_default_attributes()
    {
        return self::$default_attributes;
    }

    /**
     * Block render callback.
     *
     * @param mixed $attributes
     * @param mixed $content
     * @param mixed $block
     * @return mixed
     */
    public function render_callback($attributes, $content, $block = null)
    {
        if (is_admin()) {
            return;
        }

        $attributes = wp_parse_args($attributes, $this->get_default_attributes());

        // Extract attributes
        $block_id = isset($attributes['blockId']) ? $attributes['blockId'] : '';
        $class_hook = isset($attributes['classHook']) ? $attributes['classHook'] : '';
        $query_data = isset($attributes['queryData']) ? $attributes['queryData'] : [];
        $is_loop_builder = isset($attributes['isLoopBuilder']) ? $attributes['isLoopBuilder'] : true;

        // Build query arguments
        $query_args = array(
            'post_type' => $post_type,
            'posts_per_page' => $posts_per_page,
            'orderby' => $order_by,
            'order' => $order,
            'post_status' => 'publish',
        );

        // Handle include/exclude IDs
        if (!empty($include_ids)) {
            $include_array = array_map('trim', explode(',', $include_ids));
            $include_array = array_filter($include_array, 'is_numeric');
            if (!empty($include_array)) {
                $query_args['post__in'] = $include_array;
            }
        }

        if (!empty($exclude_ids)) {
            $exclude_array = array_map('trim', explode(',', $exclude_ids));
            $exclude_array = array_filter($exclude_array, 'is_numeric');
            if (!empty($exclude_array)) {
                $query_args['post__not_in'] = $exclude_array;
            }
        }

        // Handle sticky posts
        if (!$sticky_posts) {
            $query_args['ignore_sticky_posts'] = true;
        }

        // Handle pagination
        if ($pagination_type !== 'none') {
            $paged = get_query_var('paged') ? get_query_var('paged') : 1;
            $query_args['paged'] = $paged;
        }

        // Execute query
        $loop_query = new \WP_Query($query_args);

        // Start output buffering
        ob_start();


        if ($loop_query->have_posts()) {
?>
            <div class="eb-parent-wrapper eb-parent-<?php echo esc_attr($block_id); ?> <?php echo esc_attr($class_hook); ?>">
                <div class="eb-loop-builder-wrapper <?php echo esc_attr($block_id); ?>"
                    data-layout="<?php echo esc_attr($layout_mode); ?>"
                    data-pagination="<?php echo esc_attr($pagination_type); ?>">
                    <div class="eb-loop-builder-container <?php echo esc_attr($layout_mode); ?>">
                        <div class="eb-loop-builder-items <?php echo esc_attr($layout_mode); ?>">
                            <?php
                            while ($loop_query->have_posts()) {
                                $loop_query->the_post();
                                $current_post_id = get_the_ID();
                                $current_post_type = get_post_type();
                            ?>
                                <div class="eb-loop-item" data-post-id="<?php echo esc_attr($current_post_id); ?>">
                                    <?php
                                    // Inject context for this specific post using render_block_context filter
                                    $filter_block_context = function ($context) use ($current_post_id, $current_post_type, $block_id, $query_args) {
                                        // Add Loop Builder context
                                        $context['postId'] = $current_post_id;
                                        $context['postType'] = $current_post_type;
                                        $context['essential-blocks/postId'] = $current_post_id;
                                        $context['essential-blocks/postType'] = $current_post_type;
                                        $context['essential-blocks/queryId'] = $block_id;
                                        $context['essential-blocks/isLoopBuilder'] = true;
                                        $context['essential-blocks/query'] = $query_args;

                                        return $context;
                                    };
                                    error_log( "filter_block_context" );
                                    error_log(print_r( $filter_block_context, true));

                                    // Add the filter with high priority to ensure it's applied early
                                    add_filter('render_block_context', $filter_block_context, 1);

                                    // Render the inner blocks content for each post with context
                                    echo $this->render_inner_blocks_with_context($content, $block);

                                    // Remove the filter to prevent context leakage
                                    remove_filter('render_block_context', $filter_block_context, 1);
                                    ?>
                                </div>
                            <?php
                            }
                            ?>
                        </div>
                    </div>

                    <?php
                    // Render pagination if enabled
                    if ($pagination_type === 'numbered' && $loop_query->max_num_pages > 1) {
                        $this->render_pagination($loop_query, $pagination_type);
                    }
                    ?>
                </div>
            </div>
        <?php
        } else {
        ?>
            <div class="eb-parent-wrapper eb-parent-<?php echo esc_attr($block_id); ?> <?php echo esc_attr($class_hook); ?>">
                <div class="eb-loop-builder-wrapper <?php echo esc_attr($block_id); ?>">
                    <div class="eb-loop-builder-no-posts">
                        <p><?php esc_html_e('No posts found.', 'essential-blocks-pro'); ?></p>
                    </div>
                </div>
            </div>
            <?php
        }

        // Reset post data
        wp_reset_postdata();

        return ob_get_clean();
    }

    /**
     * Render pagination for loop builder
     *
     * @param \WP_Query $query The query object
     * @param string $pagination_type Type of pagination
     */
    private function render_pagination($query, $pagination_type)
    {
        if ($pagination_type === 'numbered') {
            $pagination_args = array(
                'total' => $query->max_num_pages,
                'current' => max(1, get_query_var('paged')),
                'format' => '?paged=%#%',
                'show_all' => false,
                'end_size' => 1,
                'mid_size' => 2,
                'prev_next' => true,
                'prev_text' => __('&laquo; Previous', 'essential-blocks-pro'),
                'next_text' => __('Next &raquo;', 'essential-blocks-pro'),
                'type' => 'array',
            );

            $pagination_links = paginate_links($pagination_args);

            if ($pagination_links) {
            ?>
                <div class="eb-loop-builder-pagination">
                    <ul class="pagination">
                        <?php
                        foreach ($pagination_links as $link) {
                            echo '<li>' . $link . '</li>';
                        }
                        ?>
                    </ul>
                </div>
<?php
            }
        }
    }

    /**
     * Render inner blocks with proper context injection
     * Following Gutenberg's post-template pattern for context sharing
     *
     * Note: Context is injected via render_block_context filter, not passed directly
     *
     * @param string $content The block content
     * @param object $block The block object
     * @return string Rendered content
     */
    private function render_inner_blocks_with_context($content, $block)
    {
        // Check if we have a proper block object with parsed_block data
        if (!$block || !isset($block->parsed_block['innerBlocks'])) {
            return $content;
        }

        $rendered_content = '';

        // Render each inner block individually to avoid Loop Builder wrapper HTML
        foreach ($block->parsed_block['innerBlocks'] as $inner_block) {
            // Create a WP_Block instance for each inner block
            // Context will be automatically injected via the render_block_context filter
            $block_instance = new \WP_Block($inner_block);
            $rendered_content .= $block_instance->render();
        }

        return $rendered_content;
    }
}
