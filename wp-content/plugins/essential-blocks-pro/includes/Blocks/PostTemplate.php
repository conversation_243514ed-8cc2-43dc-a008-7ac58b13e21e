<?php

namespace EssentialBlocks\Pro\Blocks;

use EssentialBlocks\Core\Block;

class PostTemplate extends Block
{
    protected $is_pro           = true;
    protected $editor_styles    = 'essential-blocks-pro-editor-style';
    protected $frontend_scripts = [];
    protected $frontend_styles  = [];
    protected static $default_attributes;

    public function __construct()
    {
        self::$default_attributes = [
            // Layout attributes
            'layoutMode'        => 'grid',
            
            // Responsive columns
            'columnsDesktop'    => 3,
            'columnsTab'        => 2,
            'columnsMobile'     => 1,

            // Grid gap
            'gridGapDesktop'    => 20,
            'gridGapTab'        => 20,
            'gridGapMobile'     => 20,
            'gridGapUnit'       => 'px',
        ];
    }

    /**
     * Unique name of the block.
     * @return string
     */
    public function get_name()
    {
        return 'pro-post-template';
    }

    public function get_default_attributes()
    {
        return self::$default_attributes;
    }

    /**
     * Block render callback.
     *
     * @param mixed $attributes
     * @param mixed $content
     * @param mixed $block
     * @return mixed
     */
    public function render_callback($attributes, $content, $block = null)
    {
        if (is_admin()) {
            return;
        }

        $attributes = wp_parse_args($attributes, $this->get_default_attributes());

        // Extract attributes
        $block_id = isset($attributes['blockId']) ? $attributes['blockId'] : '';
        $class_hook = isset($attributes['classHook']) ? $attributes['classHook'] : '';
        $layout_mode = isset($attributes['layoutMode']) ? $attributes['layoutMode'] : 'grid';

        // Get context from parent Loop Builder
        $context = $block->context ?? [];
        $query = $context['essential-blocks/query'] ?? $context['query'] ?? null;
        $query_id = $context['essential-blocks/queryId'] ?? $context['queryId'] ?? null;
        $is_loop_builder = $context['essential-blocks/isLoopBuilder'] ?? false;

        // If not inside Loop Builder, return placeholder
        if (!$is_loop_builder || !$query) {
            ob_start();
            ?>
            <div class="eb-parent-wrapper eb-parent-<?php echo esc_attr($block_id); ?> <?php echo esc_attr($class_hook); ?>">
                <div class="eb-post-template-placeholder">
                    <div class="eb-post-template-notice">
                        <h4><?php esc_html_e('Post Template', 'essential-blocks-pro'); ?></h4>
                        <p><?php esc_html_e('This block must be used inside a Loop Builder block to display posts.', 'essential-blocks-pro'); ?></p>
                    </div>
                </div>
            </div>
            <?php
            return ob_get_clean();
        }

        // Extract query parameters from context
        $post_type = $query['source'] ?? $query['postType'] ?? 'post';
        $posts_per_page = $query['per_page'] ?? $query['perPage'] ?? 6;
        $order_by = $query['orderby'] ?? $query['orderBy'] ?? 'date';
        $order = $query['order'] ?? 'desc';
        $offset = $query['offset'] ?? 0;

        // Build query arguments
        $query_args = array(
            'post_type' => $post_type,
            'posts_per_page' => $posts_per_page,
            'orderby' => $order_by,
            'order' => $order,
            'post_status' => 'publish',
            'offset' => $offset,
        );

        // Add include/exclude if specified
        if (!empty($query['include'])) {
            $query_args['post__in'] = is_array($query['include']) ? $query['include'] : explode(',', $query['include']);
        }
        if (!empty($query['exclude'])) {
            $query_args['post__not_in'] = is_array($query['exclude']) ? $query['exclude'] : explode(',', $query['exclude']);
        }
        if (!empty($query['author'])) {
            $query_args['author'] = is_array($query['author']) ? implode(',', $query['author']) : $query['author'];
        }

        // Execute query
        $loop_query = new \WP_Query($query_args);

        // Start output buffering
        ob_start();

        if ($loop_query->have_posts()) {
        ?>
            <div class="eb-parent-wrapper eb-parent-<?php echo esc_attr($block_id); ?> <?php echo esc_attr($class_hook); ?>">
                <div class="eb-post-template-wrapper <?php echo esc_attr($block_id); ?>"
                    data-layout="<?php echo esc_attr($layout_mode); ?>">
                    <div class="eb-post-template-container <?php echo esc_attr($layout_mode); ?>">
                        <div class="eb-post-template-items <?php echo esc_attr($layout_mode); ?>">
                            <?php
                            while ($loop_query->have_posts()) {
                                $loop_query->the_post();
                                $current_post_id = get_the_ID();
                                $current_post_type = get_post_type();
                            ?>
                                <div class="eb-post-template-item" data-post-id="<?php echo esc_attr($current_post_id); ?>">
                                    <?php
                                    // Inject context for this specific post using render_block_context filter
                                    $filter_block_context = function ($context) use ($current_post_id, $current_post_type, $query_id, $query_args) {
                                        // Add Post Template context
                                        $context['postId'] = $current_post_id;
                                        $context['postType'] = $current_post_type;
                                        $context['essential-blocks/postId'] = $current_post_id;
                                        $context['essential-blocks/postType'] = $current_post_type;
                                        $context['essential-blocks/queryId'] = $query_id;
                                        $context['essential-blocks/isLoopBuilder'] = true;
                                        $context['essential-blocks/query'] = $query_args;

                                        return $context;
                                    };

                                    // Add the filter with high priority to ensure it's applied early
                                    add_filter('render_block_context', $filter_block_context, 1);

                                    // Render the inner blocks content for each post with context
                                    echo $this->render_inner_blocks_with_context($content, $block);

                                    // Remove the filter to prevent context leakage
                                    remove_filter('render_block_context', $filter_block_context, 1);
                                    ?>
                                </div>
                            <?php
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php
        } else {
        ?>
            <div class="eb-parent-wrapper eb-parent-<?php echo esc_attr($block_id); ?> <?php echo esc_attr($class_hook); ?>">
                <div class="eb-post-template-wrapper <?php echo esc_attr($block_id); ?>">
                    <div class="eb-post-template-no-posts">
                        <p><?php esc_html_e('No posts found. Please adjust your query parameters in the Loop Builder.', 'essential-blocks-pro'); ?></p>
                    </div>
                </div>
            </div>
            <?php
        }

        // Reset post data
        wp_reset_postdata();

        return ob_get_clean();
    }

    /**
     * Render inner blocks with context
     *
     * @param string $content
     * @param object $block
     * @return string
     */
    private function render_inner_blocks_with_context($content, $block)
    {
        if (empty($content)) {
            return '';
        }

        // Parse blocks from content
        $blocks = parse_blocks($content);
        
        // Render each block
        $output = '';
        foreach ($blocks as $inner_block) {
            $output .= render_block($inner_block);
        }

        return $output;
    }
}
