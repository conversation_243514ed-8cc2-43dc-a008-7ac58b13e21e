import { __ } from "@wordpress/i18n";
import { memo } from "@wordpress/element";
import {
	InnerBlocks,
} from "@wordpress/block-editor";

import { BlockProps, withBlockContext } from "@essential-blocks/controls";

import Inspector from "./inspector";
import Style from "./style";
import defaultAttributes from "./attributes";

// Default template for Loop Builder inner blocks
const LOOP_BUILDER_TEMPLATE = [
	[
		"essential-blocks/pro-post-template",
		{},
		[
			[
				"core/group",
				{
					className: "eb-loop-item",
					layout: { type: "flex", orientation: "vertical" },
				},
				[
					["essential-blocks/advanced-image"],
					["essential-blocks/advanced-heading"],
					["essential-blocks/text"],
					["essential-blocks/post-meta"],
				],
			],
		],
	],
];

function Edit(props) {
	const { attributes, isSelected } = props;
	const {
		blockId,
		classHook,
		queryData,
	} = attributes;

	// Enhanced props for BlockProps
	const enhancedProps = {
		...props,
		blockPrefix: "eb-loop-builder",
		style: <Style {...props} />,
	};

	// Provide context values for child blocks
	const contextValue = {
		"essential-blocks/queryId": blockId,
		"essential-blocks/isLoopBuilder": true,
		"essential-blocks/query": queryData,
	};

	return (
		<>
			{isSelected && <Inspector {...props} />}
			<BlockProps.Edit {...enhancedProps}>
				<div
					className={`eb-parent-wrapper eb-parent-${blockId} ${classHook}`}
				>
					<div className={`eb-loop-builder-wrapper ${blockId}`}>
						<div className="eb-loop-builder-container">
							<div className="eb-loop-builder-content">
								<InnerBlocks
									template={LOOP_BUILDER_TEMPLATE}
									templateLock={false}
									renderAppender={InnerBlocks.ButtonBlockAppender}
									placeholder={__(
										"Add blocks here. Use the Post Template block to display posts in a loop.",
										"essential-blocks-pro",
									)}
								/>
							</div>
						</div>
					</div>
				</div>
			</BlockProps.Edit>
		</>
	);
}

export default memo(withBlockContext(defaultAttributes)(Edit));
