{"$schema": "https://schemas.wp.org/trunk/block.json", "title": "Post Template", "name": "essential-blocks/pro-post-template", "category": "essential-blocks", "description": "Contains the block elements used to render a post within a Loop Builder, like the title, date, featured image, content or excerpt, and more.", "apiVersion": 2, "textdomain": "essential-blocks-pro", "ancestor": ["essential-blocks/pro-loop-builder"], "usesContext": ["essential-blocks/queryId", "essential-blocks/isLoopBuilder", "essential-blocks/query", "queryId", "query", "displayLayout", "templateSlug", "previewPostType", "enhancedPagination", "postType"], "supports": {"reusable": false, "html": false, "align": ["wide", "full"], "layout": true, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "spacing": {"margin": true, "padding": true, "blockGap": {"__experimentalDefault": "1.25em"}, "__experimentalDefaultControls": {"blockGap": true, "padding": false, "margin": false}}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true}}, "editorScript": "essential-blocks-editor-script"}