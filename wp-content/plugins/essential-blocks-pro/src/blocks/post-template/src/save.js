import { InnerBlocks } from "@wordpress/block-editor";
import { BlockProps } from "@essential-blocks/controls";

export default function Save({ attributes }) {
    const {
        blockId,
        classHook,
        layoutMode,
    } = attributes;

    return (
        <BlockProps.Save attributes={attributes}>
            <div
                className={`eb-parent-wrapper eb-parent-${blockId} ${classHook}`}
            >
                <div
                    className={`eb-post-template-wrapper ${blockId}`}
                    data-layout={layoutMode}
                >
                    <div className={`eb-post-template-container ${layoutMode}`}>
                        {/* Template will be rendered by PHP */}
                        <div className="eb-post-template-placeholder">
                            <InnerBlocks.Content />
                        </div>
                    </div>
                </div>
            </div>
        </BlockProps.Save>
    );
}
