import {
    WRAPPER_BG,
    WRAPPER_MARGIN,
    WRAPPER_PADDING,
    WRAPPER_BORDER_SHADOW,
    LAYOUT_MODE,
    GRID_GAP,
    COLUMNS,
} from "./constants";

import {
    generateDimensionsAttributes,
    generateTypographyAttributes,
    generateBackgroundAttributes,
    generateBorderShadowAttributes,
    generateResponsiveRangeAttributes,
} from "@essential-blocks/controls";

const attributes = {
    // Unique identifier for the block
    blockId: {
        type: "string",
    },
    // CSS class hook for styling
    classHook: {
        type: "string",
    },
    // Layout mode (grid, list, etc.)
    layoutMode: {
        type: "string",
        default: "grid",
    },
    // Grid columns for layout
    ...generateResponsiveRangeAttributes(COLUMNS, {
        defaultRange: 3,
        noUnits: true,
    }),
    // Grid gap between items
    ...generateResponsiveRangeAttributes(GRID_GAP, {
        defaultRange: 20,
        defaultUnit: "px",
    }),
    // Wrapper background
    ...generateBackgroundAttributes(WRAPPER_BG, {
        defaultBgGradient: "linear-gradient(45deg,#ffffff,#ffffff)",
    }),
    // Wrapper margin
    ...generateDimensionsAttributes(WRAPPER_MARGIN),
    // Wrapper padding
    ...generateDimensionsAttributes(WRAPPER_PADDING),
    // Wrapper border & shadow
    ...generateBorderShadowAttributes(WRAPPER_BORDER_SHADOW, {
        bdrDefaults: {
            top: 0,
            bottom: 0,
            right: 0,
            left: 0,
        },
        // noShadow: true,
        // noBorder: true,
    }),
};

export default attributes;
