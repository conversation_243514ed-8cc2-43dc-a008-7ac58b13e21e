import {
    WRAPPER_BG,
    WRAPPER_MARGIN,
    WRAPPER_PADDING,
    WRAPPER_BORDER_SHADOW,
    COLUMNS,
    GRID_GAP,
} from "./constants";

import {
    softMinifyCssStrings,
    generateDimensionsControlStyles,
    generateBackgroundControlStyles,
    generateBorderShadowStyles,
    generateResponsiveRangeStyles,
    StyleComponent,
} from "@essential-blocks/controls";

export default function Style(props) {
    const { attributes, setAttributes, name } = props;
    const {
        blockId,
        layoutMode,
    } = attributes;

    // CSS selectors
    const {
        dimensionStylesDesktop: wrapperMarginDesktop,
        dimensionStylesTab: wrapperMarginTab,
        dimensionStylesMobile: wrapperMarginMobile,
    } = generateDimensionsControlStyles({
        controlName: WRAPPER_MARGIN,
        styleFor: "margin",
        attributes,
    });

    const {
        dimensionStylesDesktop: wrapperPaddingDesktop,
        dimensionStylesTab: wrapperPaddingTab,
        dimensionStylesMobile: wrapperPaddingMobile,
    } = generateDimensionsControlStyles({
        controlName: WRAPPER_PADDING,
        styleFor: "padding",
        attributes,
    });

    const {
        backgroundStylesDesktop: wrapperBackgroundStylesDesktop,
        backgroundStylesTab: wrapperBackgroundStylesTab,
        backgroundStylesMobile: wrapperBackgroundStylesMobile,
    } = generateBackgroundControlStyles({
        attributes,
        controlName: WRAPPER_BG,
    });

    const {
        styesDesktop: wrapperBDShadowDesktop,
        styesTab: wrapperBDShadowTab,
        styesMobile: wrapperBDShadowMobile,
    } = generateBorderShadowStyles({
        controlName: WRAPPER_BORDER_SHADOW,
        attributes,
    });

    const {
        rangeStylesDesktop: columnsDesktop,
        rangeStylesTab: columnsTab,
        rangeStylesMobile: columnsMobile,
    } = generateResponsiveRangeStyles({
        controlName: COLUMNS,
        property: "grid-template-columns",
        function: "repeat",
        attributes,
        customUnit: "1fr",
    });

    const {
        rangeStylesDesktop: gridGapDesktop,
        rangeStylesTab: gridGapTab,
        rangeStylesMobile: gridGapMobile,
    } = generateResponsiveRangeStyles({
        controlName: GRID_GAP,
        property: "gap",
        attributes,
    });

    // Desktop styles
    const desktopStyles = `
        .eb-post-template-wrapper.${blockId} {
            ${wrapperMarginDesktop}
            ${wrapperPaddingDesktop}
            ${wrapperBackgroundStylesDesktop}
            ${wrapperBDShadowDesktop}
        }
        
        .eb-post-template-container.${blockId}.grid {
            display: grid;
            ${columnsDesktop}
            ${gridGapDesktop}
        }
        
        .eb-post-template-container.${blockId}.list {
            display: flex;
            flex-direction: column;
            ${gridGapDesktop}
        }
    `;

    // Tablet styles
    const tabStyles = `
        .eb-post-template-wrapper.${blockId} {
            ${wrapperMarginTab}
            ${wrapperPaddingTab}
            ${wrapperBackgroundStylesTab}
            ${wrapperBDShadowTab}
        }
        
        .eb-post-template-container.${blockId}.grid {
            ${columnsTab}
            ${gridGapTab}
        }
        
        .eb-post-template-container.${blockId}.list {
            ${gridGapTab}
        }
    `;

    // Mobile styles
    const mobileStyles = `
        .eb-post-template-wrapper.${blockId} {
            ${wrapperMarginMobile}
            ${wrapperPaddingMobile}
            ${wrapperBackgroundStylesMobile}
            ${wrapperBDShadowMobile}
        }
        
        .eb-post-template-container.${blockId}.grid {
            ${columnsMobile}
            ${gridGapMobile}
        }
        
        .eb-post-template-container.${blockId}.list {
            ${gridGapMobile}
        }
    `;

    // All CSS styles
    const allStyles = softMinifyCssStrings(`
        ${desktopStyles}
        @media all and (max-width: 1024px) {
            ${tabStyles}
        }
        @media all and (max-width: 767px) {
            ${mobileStyles}
        }
    `);

    return <StyleComponent attributes={attributes} setAttributes={setAttributes} desktopAllStyles={allStyles} blockName={name} />;
}
