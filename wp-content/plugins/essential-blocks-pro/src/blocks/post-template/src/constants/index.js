// Wrapper attributes
export const WRAPPER_BG = "wrpBg";
export const WRAPPER_MARGIN = "wrpMargin";
export const WRAPPER_PADDING = "wrpPadding";
export const WRAPPER_BORDER_SHADOW = "wrpBorderShadow";

// Layout attributes
export const LAYOUT_MODE = "layoutMode";
export const COLUMNS = "columns";
export const GRID_GAP = "gridGap";

// Layout modes
export const LAYOUT_MODES = [
    { label: "Grid", value: "grid" },
    { label: "List", value: "list" },
];

// Default template for post template inner blocks
export const POST_TEMPLATE_INNER_BLOCKS = [
    [
        "core/group",
        {
            className: "eb-post-template-item",
            layout: { type: "flex", orientation: "vertical" },
        },
        [
            ["essential-blocks/advanced-image"],
            ["essential-blocks/advanced-heading"],
            ["essential-blocks/text"],
            ["essential-blocks/post-meta"],
        ],
    ],
];
