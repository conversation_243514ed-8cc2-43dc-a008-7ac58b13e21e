import { __ } from "@wordpress/i18n";
import "./style.scss";
import Edit from "./edit";
import save from "./save";
import attributes from "./attributes";
import metadata from "../block.json";
import { ReactComponent as Icon } from "./icon.svg";
import { ebConditionalRegisterBlockType } from "@essential-blocks/controls";

ebConditionalRegisterBlockType(metadata, {
    keywords: [
        __("post template", "essential-blocks-pro"),
        __("loop", "essential-blocks-pro"),
        __("template", "essential-blocks-pro"),
        __("posts", "essential-blocks-pro"),
        __("dynamic", "essential-blocks-pro"),
        __("query", "essential-blocks-pro"),
    ],
    icon: Icon,
    attributes,
    edit: Edit,
    save,
});
