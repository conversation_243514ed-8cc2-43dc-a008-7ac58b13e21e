// Post Template Block Styles

.eb-post-template-wrapper {
    position: relative;
    
    // Container styles
    .eb-post-template-container {
        &.grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        &.list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
    }
    
    // Post template items
    .eb-post-template-items {
        display: contents;
    }
    
    .eb-post-template-item {
        position: relative;
        
        &.eb-post-template-item-editable {
            // Styles for the editable first item
            border: 2px dashed transparent;
            
            &:hover {
                border-color: #007cba;
            }
        }
        
        &.eb-post-template-item-preview {
            // Styles for preview items
            opacity: 0.8;
            cursor: pointer;
            
            &:hover {
                opacity: 1;
            }
        }
    }
    
    // Loading state
    .eb-post-template-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        
        .components-spinner {
            margin-bottom: 16px;
        }
        
        p {
            margin: 0;
            color: #666;
            font-style: italic;
        }
    }
    
    // Template editing state
    .eb-post-template-template {
        border: 2px dashed #ddd;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
        
        .eb-post-template-template-header {
            margin-bottom: 20px;
            
            h4 {
                margin: 0 0 8px 0;
                color: #1e1e1e;
                font-size: 16px;
                font-weight: 600;
            }
            
            p {
                margin: 0;
                color: #666;
                font-size: 14px;
                line-height: 1.4;
            }
        }
        
        .eb-post-template-template-content {
            .block-editor-inner-blocks {
                text-align: left;
            }
        }
    }
    
    // No results state
    .eb-post-template-no-results {
        padding: 20px;
        text-align: center;
        
        p {
            margin: 0;
            font-size: 14px;
        }
    }
    
    // Placeholder state (when not in Loop Builder)
    .eb-post-template-placeholder {
        border: 2px dashed #ddd;
        border-radius: 4px;
        padding: 40px 20px;
        text-align: center;
        
        .eb-post-template-notice {
            h4 {
                margin: 0 0 8px 0;
                color: #1e1e1e;
                font-size: 16px;
                font-weight: 600;
            }
            
            p {
                margin: 0;
                color: #666;
                font-size: 14px;
                line-height: 1.4;
            }
        }
    }
    
    // Block preview styles
    .eb-post-template-block-preview {
        pointer-events: none;
        
        &:hover {
            outline: 2px solid #007cba;
            outline-offset: 2px;
        }
    }
}

// Editor specific styles
.block-editor-block-list__layout {
    .eb-post-template-wrapper {
        // Ensure proper spacing in editor
        margin-bottom: 20px;
    }
}

// Context info styles
.eb-post-template-context-info {
    .eb-control-label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
    }
}
