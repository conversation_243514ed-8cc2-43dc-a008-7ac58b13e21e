import { __ } from "@wordpress/i18n";
import { memo, useMemo } from "@wordpress/element";
import {
    InnerBlocks,
    BlockContextProvider,
    __experimentalUseBlockPreview as useBlockPreview,
    store as blockEditorStore,
} from "@wordpress/block-editor";
import { useSelect } from "@wordpress/data";
import { useEntityRecords } from "@wordpress/core-data";
import { Spinner } from "@wordpress/components";

import { BlockProps, withBlockContext } from "@essential-blocks/controls";

import Inspector from "./inspector";
import Style from "./style";
import defaultAttributes from "./attributes";
import { POST_TEMPLATE_INNER_BLOCKS } from "./constants";

// Block Preview Component for non-editable post items
function BlockPreview({ blocks, contextId, setActivePostId, isHidden }) {
    const blockPreviewProps = useBlockPreview({
        blocks,
    });

    const handleOnClick = () => setActivePostId && setActivePostId(contextId);

    const handleKeyDown = (event) => {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            setActivePostId && setActivePostId(contextId);
        }
    };

    const style = {
        display: isHidden ? "none" : undefined,
    };

    return (
        <div
            {...blockPreviewProps}
            className="block-editor-inner-blocks eb-post-template-block-preview"
            tabIndex={0}
            role="button"
            onClick={handleOnClick}
            onKeyDown={handleKeyDown}
            style={style}
        />
    );
}

const MemoizedBlockPreview = memo(BlockPreview);

function Edit(props) {
    const { attributes, isSelected, context, clientId } = props;
    const {
        blockId,
        classHook,
        layoutMode,
    } = attributes;

    // Get context from parent Loop Builder
    const queryId = context?.["essential-blocks/queryId"] || context?.queryId;
    const query = context?.["essential-blocks/query"] || context?.query;
    const isLoopBuilder = context?.["essential-blocks/isLoopBuilder"];

    // Enhanced props for BlockProps
    const enhancedProps = {
        ...props,
        blockPrefix: "eb-post-template",
        style: <Style {...props} />,
    };

    // Get inner blocks for preview
    const innerBlocks = useSelect((select) => {
        return select("core/block-editor")?.getBlocks(clientId);
    }, [clientId]);

    // If not inside Loop Builder, show placeholder
    if (!isLoopBuilder || !query) {
        return (
            <>
                {isSelected && <Inspector {...props} />}
                <BlockProps.Edit {...enhancedProps}>
                    <div className={`eb-parent-wrapper eb-parent-${blockId} ${classHook}`}>
                        <div className="eb-post-template-placeholder">
                            <div className="eb-post-template-notice">
                                <h4>{__("Post Template", "essential-blocks-pro")}</h4>
                                <p>
                                    {__(
                                        "This block must be used inside a Loop Builder block to display posts.",
                                        "essential-blocks-pro"
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                </BlockProps.Edit>
            </>
        );
    }

    // Extract query parameters from context
    const postType = query?.source || query?.postType || "post";
    const postsPerPage = query?.per_page || query?.perPage || 6;
    const orderBy = query?.orderby || query?.orderBy || "date";
    const order = query?.order || "desc";
    const offset = query?.offset || 0;

    // Build query parameters for useEntityRecords
    const queryParams = {
        per_page: postsPerPage,
        orderby: orderBy,
        order: order,
        offset: offset,
        _embed: true,
    };

    // Add include/exclude if specified
    if (query?.include && query.include.length > 0) {
        queryParams.include = query.include;
    }
    if (query?.exclude && query.exclude.length > 0) {
        queryParams.exclude = query.exclude;
    }
    if (query?.author && query.author.length > 0) {
        queryParams.author = query.author;
    }

    // Fetch posts using useEntityRecords
    const { records: fetchedPosts, isResolving: isLoadingPosts } =
        useEntityRecords("postType", postType, queryParams);

    // Use fetched posts
    const posts = useMemo(() => {
        return fetchedPosts || [];
    }, [fetchedPosts]);

    // Loading state
    const isLoading = isLoadingPosts;

    return (
        <>
            {isSelected && <Inspector {...props} />}
            <BlockProps.Edit {...enhancedProps}>
                <div className={`eb-parent-wrapper eb-parent-${blockId} ${classHook}`}>
                    <div className={`eb-post-template-wrapper ${blockId}`}>
                        <div className={`eb-post-template-container ${layoutMode}`}>
                            {/* Loading State */}
                            {isLoading && (
                                <div className="eb-post-template-loading">
                                    <Spinner />
                                    <p>
                                        {__(
                                            "Loading posts...",
                                            "essential-blocks-pro",
                                        )}
                                    </p>
                                </div>
                            )}

                            {/* Post Template Items */}
                            {posts.length > 0 ? (
                                <div className="eb-post-template-items">
                                    {posts.map((post, index) => {
                                        // Only the first item is editable, others show preview
                                        const isFirstItem = index === 0;
                                        return (
                                            <div
                                                key={post.id}
                                                className={`eb-post-template-item ${
                                                    isFirstItem ? "eb-post-template-item-editable" : "eb-post-template-item-preview"
                                                }`}
                                            >
                                                <BlockContextProvider
                                                    value={{
                                                        "essential-blocks/postId": post.id,
                                                        "essential-blocks/postType": post.type,
                                                        "essential-blocks/queryId": queryId,
                                                        "essential-blocks/isLoopBuilder": true,
                                                        // Also provide standard context keys for compatibility
                                                        postId: post.id,
                                                        postType: post.type,
                                                    }}
                                                >
                                                    {isFirstItem ? (
                                                        <InnerBlocks
                                                            template={POST_TEMPLATE_INNER_BLOCKS}
                                                            templateLock={false}
                                                            renderAppender={InnerBlocks.ButtonBlockAppender}
                                                        />
                                                    ) : (
                                                        <MemoizedBlockPreview
                                                            blocks={innerBlocks}
                                                            contextId={post.id}
                                                            setActivePostId={() => {}} // No-op for preview items
                                                            isHidden={false}
                                                        />
                                                    )}
                                                </BlockContextProvider>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                !isLoading && (
                                    <div className="eb-post-template-template">
                                        <div className="eb-post-template-template-header">
                                            <h4>
                                                {__(
                                                    "Post Template",
                                                    "essential-blocks-pro",
                                                )}
                                            </h4>
                                            <p>
                                                {__(
                                                    "Add blocks here to build your post template. This template will be repeated for each post in your query.",
                                                    "essential-blocks-pro",
                                                )}
                                            </p>
                                        </div>
                                        <div className="eb-post-template-template-content">
                                            <InnerBlocks
                                                template={POST_TEMPLATE_INNER_BLOCKS}
                                                templateLock={false}
                                                renderAppender={InnerBlocks.ButtonBlockAppender}
                                                placeholder={__(
                                                    "Drag blocks here to build your post template.",
                                                    "essential-blocks-pro",
                                                )}
                                            />
                                        </div>
                                    </div>
                                )
                            )}

                            {posts.length === 0 && !isLoading && (
                                <div className="eb-post-template-no-results">
                                    <p
                                        style={{
                                            color: "#d63638",
                                            fontStyle: "italic",
                                        }}
                                    >
                                        {__(
                                            "No posts found. Please adjust your query parameters in the Loop Builder.",
                                            "essential-blocks-pro",
                                        )}
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </BlockProps.Edit>
        </>
    );
}

export default memo(withBlockContext(defaultAttributes)(Edit));
