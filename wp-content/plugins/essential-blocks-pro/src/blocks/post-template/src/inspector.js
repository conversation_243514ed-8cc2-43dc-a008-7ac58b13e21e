import { __ } from "@wordpress/i18n";
import {
    SelectControl,
    ButtonGroup,
    Button,
} from "@wordpress/components";

import {
    ResponsiveRangeC<PERSON>roller,
    InspectorP<PERSON><PERSON>,
} from "@essential-blocks/controls";

import {
    WRAPPER_BG,
    WRAPPER_MARGIN,
    WRAPPER_PADDING,
    WRAPPER_BORDER_SHADOW,
    COLUMNS,
    GRID_GAP,
    LAYOUT_MODES,
} from "./constants";

const Inspector = ({ attributes, setAttributes, context }) => {
    const {
        layoutMode,
    } = attributes;

    // Get context from parent Loop Builder
    const isLoopBuilder = context?.["essential-blocks/isLoopBuilder"];
    const query = context?.["essential-blocks/query"] || context?.query;

    return (
        <InspectorPanel
            advancedControlProps={{
                marginPrefix: WRAPPER_MARGIN,
                paddingPrefix: WRAPPER_PADDING,
                backgroundPrefix: WRAPPER_BG,
                borderPrefix: WRAPPER_BORDER_SHADOW,
                hasMargin: true,
            }}
        >
            <InspectorPanel.General>
                {/* Context Information */}
                {isLoopBuilder && query && (
                    <div className="eb-post-template-context-info" style={{ marginBottom: '16px' }}>
                        <div style={{ 
                            padding: '12px', 
                            backgroundColor: '#f0f6fc', 
                            border: '1px solid #c3e4f7',
                            borderRadius: '4px',
                            fontSize: '12px'
                        }}>
                            <strong>{__("Connected to Loop Builder", "essential-blocks-pro")}</strong>
                            <br />
                            {__("Post Type: ", "essential-blocks-pro")} 
                            <code>{query?.source || query?.postType || 'post'}</code>
                            <br />
                            {__("Posts Per Page: ", "essential-blocks-pro")} 
                            <code>{query?.per_page || query?.perPage || 6}</code>
                        </div>
                    </div>
                )}

                {/* Layout Mode */}
                <div className="eb-control-item-wrapper">
                    <label className="eb-control-label">
                        {__("Layout Mode", "essential-blocks-pro")}
                    </label>
                    <ButtonGroup>
                        {LAYOUT_MODES.map((mode) => (
                            <Button
                                key={mode.value}
                                isPressed={layoutMode === mode.value}
                                onClick={() => setAttributes({ layoutMode: mode.value })}
                            >
                                {mode.label}
                            </Button>
                        ))}
                    </ButtonGroup>
                </div>

                {/* Grid Columns (only for grid layout) */}
                {layoutMode === "grid" && (
                    <ResponsiveRangeController
                        baseLabel={__("Columns", "essential-blocks-pro")}
                        controlName={COLUMNS}
                        min={1}
                        max={6}
                        step={1}
                        noUnits={true}
                    />
                )}

                {/* Grid Gap */}
                <ResponsiveRangeController
                    baseLabel={__("Gap", "essential-blocks-pro")}
                    controlName={GRID_GAP}
                    min={0}
                    max={100}
                    step={1}
                />
            </InspectorPanel.General>

            <InspectorPanel.Style>
                {/* Additional style controls can be added here */}
                <div className="eb-control-item-wrapper">
                    <p style={{ fontStyle: 'italic', color: '#666' }}>
                        {__(
                            "Style individual post elements by selecting them within the template.",
                            "essential-blocks-pro"
                        )}
                    </p>
                </div>
            </InspectorPanel.Style>
        </InspectorPanel>
    );
};

export default Inspector;
