{"id-94034": {"title": "Travel Blogger &amp; Influencer", "id": 94034, "publish-date": 1735897077, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-600x1939.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-400x1292.jpg", "astra-site-url": "//websitedemos.net/travel-blogger-07", "astra-site-parent-id": 3649, "astra-sites-tag": {"1652": "blogger", "1654": "content-creator", "1547": "influencer", "2163": "social-media-influencer", "2168": "tourism-influencers", "2170": "travel-tourism", "750": "travel-blog", "1683": "travel-blogger", "2167": "travel-influencer", "2169": "travel-vlogger"}, "astra-sites-type": "agency-mini", "astra-site-category": {"37": "blog"}, "astra-sites-template-category": {"2828": "blog", "2770": "personal", "2768": "portfolio", "2775": "travel-tourism", "2801": "video", "2870": "vlog"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2497": "blog", "3429": "premium"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "Awe-inspiring website template for bloggers and influencers to share travel experiences", "pages": {"id-94038": {"title": "Destination", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-600x689.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/607", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/destination/", "astra-sites-tag": {"785": "destination"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94039": {"title": "Europe", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-europe.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-europe-600x697.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/609", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/europe/", "astra-sites-tag": {"2014": "europe"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94040": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-600x1939.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/611", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-94041": {"title": "The Americas", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-america.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-america-600x707.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/613", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/the-americas/", "astra-sites-tag": {"2015": "america"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94042": {"title": "Videos", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-video.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-video-600x697.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/615", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/videos/", "astra-sites-tag": {"3066": "video"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94043": {"title": "Work with me", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-contact-600x640.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/617", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/work-with-me/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-94036": {"title": "Africa", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-africa.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-africa-600x700.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/603", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/africa/", "astra-sites-tag": {"2019": "africa"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94037": {"title": "Asia", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-asia.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-destination-asia-600x707.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/605", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/asia/", "astra-sites-tag": {"2017": "asia"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94035": {"title": "About me", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2025/01/travel-blogger-07-about-600x1189.jpg", "astra-page-api-url": "https://websitedemos.net/travel-blogger-07/wp-json/wp/v2/pages/600", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/travel-blogger-07/about-me/", "astra-sites-tag": {"1007": "about-me"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86476": {"title": "eCourse", "id": 86476, "publish-date": 1708544712, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-600x3981.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-400x2654.jpg", "astra-site-url": "//websitedemos.net/teach-ecourse-07", "astra-site-parent-id": 3500, "astra-sites-tag": {"2562": "code-course", "462": "courses", "469": "ecourse", "468": "elearning", "719": "online-coaching", "466": "online-courses", "975": "online-learning", "1844": "premium", "721": "training", "720": "tutorials"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business", "36": "other"}, "astra-sites-template-category": {"2825": "coaching", "2835": "elearning", "2880": "membership", "2827": "online-course"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2494": "elearning"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86479": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-600x3981.jpg", "astra-page-api-url": "https://websitedemos.net/teach-ecourse-07/wp-json/wp/v2/pages/1324", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/teach-ecourse-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "139": "other"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86477": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-about-600x1723.jpg", "astra-page-api-url": "https://websitedemos.net/teach-ecourse-07/wp-json/wp/v2/pages/1328", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/teach-ecourse-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "139": "other"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86480": {"title": "Lessons", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-lesson.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-lesson-600x1813.jpg", "astra-page-api-url": "https://websitedemos.net/teach-ecourse-07/wp-json/wp/v2/pages/1326", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/teach-ecourse-07/lessons/", "astra-sites-tag": {"747": "lessons"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "139": "other"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86478": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/teach-ecourses-07-contact-600x998.jpg", "astra-page-api-url": "https://websitedemos.net/teach-ecourse-07/wp-json/wp/v2/pages/1330", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/teach-ecourse-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "139": "other"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-75363": {"title": "Books Store", "id": 75363, "publish-date": 1690996492, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-600x2527.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-400x1685.jpg", "astra-site-url": "//websitedemos.net/books-store-08", "astra-site-parent-id": 3368, "astra-sites-tag": {"724": "author", "2355": "author-book-store", "2354": "author-portfolio", "2353": "book-writer", "2352": "books-store", "496": "ecommerce", "383": "online-store"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": {"2839": "author-writer", "2814": "bookstore", "2769": "ecommerce", "2770": "personal"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-75367": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-600x2527.jpg", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/1559", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/books-store-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75364": {"title": "About Author", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-about-author.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-about-author-600x1653.jpg", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/1572", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/books-store-08/about-author/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-75365": {"title": "Books", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-books.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-books-600x1387.jpg", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/1570", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/books-store-08/books/", "astra-sites-tag": {"1740": "books"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-85090": {"title": "Blog", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/1574", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/books-store-08/blog/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": ""}, "id-75505": {"title": "Shop", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/197", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/books-store-08/shop/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75503": {"title": "Checkout", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/194", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/books-store-08/checkout/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75504": {"title": "Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/195", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/books-store-08/customer-dashboard/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75366": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/books-stores-08-contact-600x967.jpg", "astra-page-api-url": "https://websitedemos.net/books-store-08/wp-json/wp/v2/pages/1576", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/books-store-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": 59789, "ecommerce_parent_template": ""}, "id-59789": {"title": "Book Store", "id": 59789, "publish-date": 1661251526, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-600x2546.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-400x1697.jpg", "astra-site-url": "//websitedemos.net/book-store-08", "astra-site-parent-id": 2657, "astra-sites-tag": {"724": "author", "2355": "author-book-store", "2354": "author-portfolio", "2353": "book-writer", "2352": "books-store", "496": "ecommerce", "383": "online-store", "1024": "woocommerce"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-59793": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-600x2546.jpg", "astra-page-api-url": "https://websitedemos.net/book-store-08/wp-json/wp/v2/pages/8", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/book-store-08/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-59790": {"title": "About Author", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-about-author.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-about-author-600x1597.jpg", "astra-page-api-url": "https://websitedemos.net/book-store-08/wp-json/wp/v2/pages/12", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/book-store-08/about-author/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59791": {"title": "Books", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-books.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-books-600x1400.jpg", "astra-page-api-url": "https://websitedemos.net/book-store-08/wp-json/wp/v2/pages/10", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/book-store-08/books/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-59792": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/book-store-08-contact-600x931.jpg", "astra-page-api-url": "https://websitedemos.net/book-store-08/wp-json/wp/v2/pages/16", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/book-store-08/contact/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": 75363, "ecommerce_parent_template": 75363}, "id-86459": {"title": "Pub and Bar", "id": 86459, "publish-date": 1708714084, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-600x3024.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-400x2016.jpg", "astra-site-url": "//websitedemos.net/pub-and-bar-07", "astra-site-parent-id": 3498, "astra-sites-tag": {"2009": "bar-room", "2007": "barbeque-restaurant", "2010": "beer-bar", "1988": "beer-joint", "562": "bistro", "635": "brewery", "1989": "cocktail-lounge", "882": "drinks", "350": "hotel", "657": "lounge", "1844": "premium", "1983": "pub", "2008": "pub-and-bar", "335": "restaurant"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2769": "ecommerce", "2785": "food", "2776": "restaurant", "2813": "winery"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"3430": "dark", "2496": "restaurant-food"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86464": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-600x3024.jpg", "astra-page-api-url": "https://websitedemos.net/pub-and-bar-07/wp-json/wp/v2/pages/1423", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pub-and-bar-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86460": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-about-us-600x1763.jpg", "astra-page-api-url": "https://websitedemos.net/pub-and-bar-07/wp-json/wp/v2/pages/1426", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pub-and-bar-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86462": {"title": "Drinks", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-drinks.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-drinks-600x2935.jpg", "astra-page-api-url": "https://websitedemos.net/pub-and-bar-07/wp-json/wp/v2/pages/1429", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pub-and-bar-07/drinks/", "astra-sites-tag": {"1965": "drink"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86463": {"title": "Food", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-food.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-food-600x2910.jpg", "astra-page-api-url": "https://websitedemos.net/pub-and-bar-07/wp-json/wp/v2/pages/1431", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pub-and-bar-07/food/", "astra-sites-tag": {"336": "food"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86465": {"title": "Store", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-store.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-store-600x1498.jpg", "astra-page-api-url": "https://websitedemos.net/pub-and-bar-07/wp-json/wp/v2/pages/1433", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pub-and-bar-07/store/", "astra-sites-tag": {"749": "store"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86461": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/pub-and-bar-07-contact-600x1025.jpg", "astra-page-api-url": "https://websitedemos.net/pub-and-bar-07/wp-json/wp/v2/pages/1435", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pub-and-bar-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-93908": {"title": "Conference Event", "id": 93908, "publish-date": 1734515686, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/conference-event-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/conference-event-08-600x2411.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/conference-event-08-400x1607.jpg", "astra-site-url": "//websitedemos.net/conference-event-08", "astra-site-parent-id": 3645, "astra-sites-tag": {"744": "conference", "742": "event-website", "743": "meeting", "399": "one-page", "745": "organization", "463": "single-page"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": {"2811": "conference", "2784": "event", "2771": "one-page"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2489": "event-invitation", "3428": "free", "2927": "one-page"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "Accessible, modern template ideal for conferences or event company websites", "pages": {"id-93909": {"title": "One Page Website", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/conference-event-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/conference-event-08-600x2411.jpg", "astra-page-api-url": "https://websitedemos.net/conference-event-08/wp-json/wp/v2/pages/181", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/conference-event-08/", "astra-sites-tag": {"463": "single-page"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-59775": {"title": "Digital Agency", "id": 59775, "publish-date": 1661176634, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-600x2216.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-400x1477.jpg", "astra-site-url": "//websitedemos.net/agency-08", "astra-site-parent-id": 2656, "astra-sites-tag": {"663": "agency", "979": "company", "686": "corporate", "664": "digital-services", "812": "office", "665": "service-agency"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2487": "agency"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-101955": {"title": "Customer Cabinet", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/agency-08/wp-json/wp/v2/pages/3225", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/agency-08/customer-cabinet/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": ""}, "id-59778": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-600x2216.jpg", "astra-page-api-url": "https://websitedemos.net/agency-08/wp-json/wp/v2/pages/1488", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/agency-08/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59776": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-about-600x1426.jpg", "astra-page-api-url": "https://websitedemos.net/agency-08/wp-json/wp/v2/pages/1490", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/agency-08/about/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59779": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-services-600x1322.jpg", "astra-page-api-url": "https://websitedemos.net/agency-08/wp-json/wp/v2/pages/1492", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/agency-08/services/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59777": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/agency-08-contact-600x829.jpg", "astra-page-api-url": "https://websitedemos.net/agency-08/wp-json/wp/v2/pages/1494", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/agency-08/contact/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86435": {"title": "Psychiatrist", "id": 86435, "publish-date": 1707844169, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/psychiatrist-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/psychiatrist-07-600x2996.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/psychiatrist-07-400x1997.jpg", "astra-site-url": "//websitedemos.net/psychiatrist-07", "astra-site-parent-id": 3497, "astra-sites-tag": {"608": "guidance", "399": "one-page", "1020": "personal", "621": "psychiatrist", "622": "psychotherapy", "463": "single-page", "619": "wellness"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2867": "doctor", "2834": "healthcare", "2770": "personal", "2831": "service", "2833": "therapy-psychologist"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2490": "healthcare"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86436": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/psychiatrist-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/psychiatrist-07-600x2996.jpg", "astra-page-api-url": "https://websitedemos.net/psychiatrist-07/wp-json/wp/v2/pages/2", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/psychiatrist-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-59741": {"title": "Covid 19 Prevention", "id": 59741, "publish-date": 1703934324, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-600x2259.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-400x1506.jpg", "astra-site-url": "//websitedemos.net/covid-prevention-08", "astra-site-parent-id": 2654, "astra-sites-tag": {"2556": "avoidance", "2554": "covid", "2555": "covid-prevention", "2558": "disease-prevention", "2557": "prohibition"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2490": "healthcare"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-59744": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-600x2259.jpg", "astra-page-api-url": "https://websitedemos.net/covid-prevention-08/wp-json/wp/v2/pages/9", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/covid-prevention-08/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59743": {"title": "Contagion", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-contagion.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-contagion-600x1709.jpg", "astra-page-api-url": "https://websitedemos.net/covid-prevention-08/wp-json/wp/v2/pages/1561", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/covid-prevention-08/contagion/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59746": {"title": "Symptoms", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-symptoms.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-symptoms-600x1656.jpg", "astra-page-api-url": "https://websitedemos.net/covid-prevention-08/wp-json/wp/v2/pages/1562", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/covid-prevention-08/symptoms/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59745": {"title": "Prevention", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-prevention.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-prevention-600x2003.jpg", "astra-page-api-url": "https://websitedemos.net/covid-prevention-08/wp-json/wp/v2/pages/1300", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/covid-prevention-08/prevention/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59742": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-contact-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/covid-prevention-08-contact-us-600x1120.jpg", "astra-page-api-url": "https://websitedemos.net/covid-prevention-08/wp-json/wp/v2/pages/1563", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/covid-prevention-08/contact-us/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86076": {"title": "Makeup Artist Studio", "id": 86076, "publish-date": 1707839331, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-600x1877.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-400x1251.jpg", "astra-site-url": "//websitedemos.net/makeup-artist-07", "astra-site-parent-id": 3471, "astra-sites-tag": {"579": "bridal-make-up", "477": "hair-care", "575": "hair-styles", "1126": "make-up", "1128": "make-up-artist", "1127": "skin-care"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2790": "beauty-fashion", "2821": "feminine-girly", "2890": "makeup-cosmetic", "2770": "personal", "2837": "salon"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2479": "beauty", "3430": "dark", "3434": "feminine"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86085": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-600x1877.jpg", "astra-page-api-url": "https://websitedemos.net/makeup-artist-07/wp-json/wp/v2/pages/708", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/makeup-artist-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86087": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-services-600x1881.jpg", "astra-page-api-url": "https://websitedemos.net/makeup-artist-07/wp-json/wp/v2/pages/713", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/makeup-artist-07/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86083": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-about-600x1249.jpg", "astra-page-api-url": "https://websitedemos.net/makeup-artist-07/wp-json/wp/v2/pages/711", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/makeup-artist-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86086": {"title": "Pricing", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-pricing.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-pricing-600x1277.jpg", "astra-page-api-url": "https://websitedemos.net/makeup-artist-07/wp-json/wp/v2/pages/715", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/makeup-artist-07/pricing/", "astra-sites-tag": {"585": "pricing"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86084": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/makeup-artist-07-contact-600x1174.jpg", "astra-page-api-url": "https://websitedemos.net/makeup-artist-07/wp-json/wp/v2/pages/717", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/makeup-artist-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-59233": {"title": "Coffee House", "id": 59233, "publish-date": 1661278258, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-600x2042.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-400x1361.jpg", "astra-site-url": "//websitedemos.net/coffee-house-08", "astra-site-parent-id": 2585, "astra-sites-tag": {"693": "cafe", "2042": "cafe-bar", "1527": "cafeteria", "2043": "coffee-house", "1575": "coffee-shop", "2421": "purchases", "2417": "reseller", "335": "restaurant", "2041": "snack-bar"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": [], "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-59236": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-600x2042.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-house-08/wp-json/wp/v2/pages/514", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-house-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59234": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-about-600x1733.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-house-08/wp-json/wp/v2/pages/516", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-house-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59237": {"title": "<PERSON><PERSON>", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-menu.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-menu-600x1102.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-house-08/wp-json/wp/v2/pages/518", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-house-08/menu/", "astra-sites-tag": {"565": "menu"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59235": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/08/coffee-house-08-contact-600x1114.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-house-08/wp-json/wp/v2/pages/520", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-house-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86066": {"title": "Luxury Hotel", "id": 86066, "publish-date": 1707829135, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-600x3201.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-400x2134.jpg", "astra-site-url": "//websitedemos.net/luxury-hotel-07", "astra-site-parent-id": 3470, "astra-sites-tag": {"2073": "accommodation", "1534": "bnb", "2078": "business-hotel", "350": "hotel", "2082": "luxury-hotel", "2197": "luxury-residence", "1844": "premium", "714": "resort", "430": "tourism", "427": "travel"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2785": "food", "2788": "hotel-bnb", "2775": "travel-tourism"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": [], "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86072": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-600x3201.jpg", "astra-page-api-url": "https://websitedemos.net/luxury-hotel-07/wp-json/wp/v2/pages/1922", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/luxury-hotel-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86067": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-about-600x1628.jpg", "astra-page-api-url": "https://websitedemos.net/luxury-hotel-07/wp-json/wp/v2/pages/1923", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/luxury-hotel-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86070": {"title": "Dining", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-dining.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-dining-600x2116.jpg", "astra-page-api-url": "https://websitedemos.net/luxury-hotel-07/wp-json/wp/v2/pages/1924", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/luxury-hotel-07/dining/", "astra-sites-tag": {"1798": "dining"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86068": {"title": "Accommodation", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-accommodation.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-accommodation-600x2132.jpg", "astra-page-api-url": "https://websitedemos.net/luxury-hotel-07/wp-json/wp/v2/pages/1925", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/luxury-hotel-07/accommodation/", "astra-sites-tag": {"2073": "accommodation"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86071": {"title": "Facilities &#038; Amenities", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-facilities.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-facilities-600x2329.jpg", "astra-page-api-url": "https://websitedemos.net/luxury-hotel-07/wp-json/wp/v2/pages/1926", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/luxury-hotel-07/facilities-amenities/", "astra-sites-tag": {"2315": "facilities-amenities"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86069": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-contact-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/luxury-hotel-07-contact-us-600x1345.jpg", "astra-page-api-url": "https://websitedemos.net/luxury-hotel-07/wp-json/wp/v2/pages/1927", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/luxury-hotel-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-59202": {"title": "Limousine Rental Agency", "id": 59202, "publish-date": 1669231673, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-600x3360.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-400x2240.jpg", "astra-site-url": "//websitedemos.net/rental-agency-08", "astra-site-parent-id": 2582, "astra-sites-tag": {"2540": "limousine", "2538": "rent-limousines", "2539": "rent-services", "2537": "rental-agency", "2541": "sedan"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": [], "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-59205": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-600x3360.jpg", "astra-page-api-url": "https://websitedemos.net/rental-agency-08/wp-json/wp/v2/pages/576", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/rental-agency-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59203": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-about-us-600x2154.jpg", "astra-page-api-url": "https://websitedemos.net/rental-agency-08/wp-json/wp/v2/pages/578", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/rental-agency-08/about-us/", "astra-sites-tag": {"455": "about-us"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59207": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-services-600x2575.jpg", "astra-page-api-url": "https://websitedemos.net/rental-agency-08/wp-json/wp/v2/pages/582", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/rental-agency-08/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59206": {"title": "Our Fleet", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-our-fleet.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-our-fleet-600x2014.jpg", "astra-page-api-url": "https://websitedemos.net/rental-agency-08/wp-json/wp/v2/pages/580", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/rental-agency-08/our-fleet/", "astra-sites-tag": {"2583": "our-fleet"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59204": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-save.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/11/rental-agency-08-save-600x1413.jpg", "astra-page-api-url": "https://websitedemos.net/rental-agency-08/wp-json/wp/v2/pages/584", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/rental-agency-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86407": {"title": "Financial Advisors", "id": 86407, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-600x2178.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-400x1452.jpg", "astra-site-url": "//websitedemos.net/financial-accounting-07", "astra-site-parent-id": 3495, "astra-sites-tag": {"447": "accountant", "448": "accounting", "581": "advice", "449": "chartered-accountant", "446": "finance", "582": "financial-investment", "775": "investment-banking", "1844": "premium"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2823": "accounting", "2777": "business", "2805": "finance", "2866": "insurance", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2481": "finance-service"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86410": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-600x2178.jpg", "astra-page-api-url": "https://websitedemos.net/financial-accounting-07/wp-json/wp/v2/pages/883", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/financial-accounting-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86412": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-services-600x1430.jpg", "astra-page-api-url": "https://websitedemos.net/financial-accounting-07/wp-json/wp/v2/pages/886", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/financial-accounting-07/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86411": {"title": "Industries", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-industry.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-industry-600x1334.jpg", "astra-page-api-url": "https://websitedemos.net/financial-accounting-07/wp-json/wp/v2/pages/888", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/financial-accounting-07/industries/", "astra-sites-tag": {"493": "industries"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86408": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-about-600x1686.jpg", "astra-page-api-url": "https://websitedemos.net/financial-accounting-07/wp-json/wp/v2/pages/890", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/financial-accounting-07/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86409": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/financial-advisor-07-contact-600x938.jpg", "astra-page-api-url": "https://websitedemos.net/financial-accounting-07/wp-json/wp/v2/pages/892", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/financial-accounting-07/contact-us/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48121": {"title": "Organic Store", "id": 48121, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08-600x2164.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08-400x1442.jpg", "astra-site-url": "//websitedemos.net/organic-shop-08", "astra-site-parent-id": 1702, "astra-sites-tag": {"2418": "commerce", "1017": "e-commerce", "496": "ecommerce", "2419": "eshopping", "2424": "health-food", "2425": "lite-food", "369": "natural", "2215": "online-marketing", "2420": "online-shopping", "383": "online-store", "368": "organic", "2423": "organic-food", "967": "product-store", "2417": "reseller", "733": "shop", "2416": "shopping", "749": "store", "2426": "vegetarian-food", "966": "woo-commerce", "1024": "woocommerce"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-48124": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08-600x2164.jpg", "astra-page-api-url": "https://websitedemos.net/organic-shop-08/wp-json/wp/v2/pages/3610", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/organic-shop-08/", "astra-sites-tag": {"814": "homepage"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-48122": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08-about-600x1246.jpg", "astra-page-api-url": "https://websitedemos.net/organic-shop-08/wp-json/wp/v2/pages/3612", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/organic-shop-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48123": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/organic-shop-08-contact-600x832.jpg", "astra-page-api-url": "https://websitedemos.net/organic-shop-08/wp-json/wp/v2/pages/3614", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/organic-shop-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86050": {"title": "Pearson College", "id": 86050, "publish-date": 1707764565, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-600x2222.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-400x1481.jpg", "astra-site-url": "//websitedemos.net/education-07", "astra-site-parent-id": 3468, "astra-sites-tag": {"457": "college", "459": "education", "460": "learning", "1844": "premium", "456": "school", "458": "university"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2825": "coaching", "2840": "college-university", "2772": "education"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2491": "educational-institutes"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86055": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-600x2222.jpg", "astra-page-api-url": "https://websitedemos.net/education-07/wp-json/wp/v2/pages/268", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/education-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86053": {"title": "Courses", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-courses.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-courses-600x1817.jpg", "astra-page-api-url": "https://websitedemos.net/education-07/wp-json/wp/v2/pages/270", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/education-07/courses/", "astra-sites-tag": {"462": "courses"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86051": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-about-600x1777.jpg", "astra-page-api-url": "https://websitedemos.net/education-07/wp-json/wp/v2/pages/269", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/education-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86056": {"title": "Teachers", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-teachers.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-teachers-600x1117.jpg", "astra-page-api-url": "https://websitedemos.net/education-07/wp-json/wp/v2/pages/271", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/education-07/teachers/", "astra-sites-tag": {"464": "teacher"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86054": {"title": "Gallery", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-gallery.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-gallery-600x1703.jpg", "astra-page-api-url": "https://websitedemos.net/education-07/wp-json/wp/v2/pages/272", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/education-07/gallery/", "astra-sites-tag": {"461": "gallery"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86052": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/education-07-contact-600x1022.jpg", "astra-page-api-url": "https://websitedemos.net/education-07/wp-json/wp/v2/pages/273", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/education-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-58561": {"title": "Fashion Designer", "id": 58561, "publish-date": 1634042866, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-600x2900.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-400x1933.jpg", "astra-site-url": "//websitedemos.net/fashion-designer-boutique-08", "astra-site-parent-id": 2452, "astra-sites-tag": {"2105": "boutique", "2418": "commerce", "2350": "designer-cloths-store", "496": "ecommerce", "2419": "eshopping", "2327": "fashion-designer", "2349": "fashion-designer-boutique", "2351": "fashion-designer-portfolio", "2422": "online-market", "732": "online-shop", "2420": "online-shopping", "383": "online-store", "444": "portfolio", "2421": "purchases", "2417": "reseller", "733": "shop", "2416": "shopping", "749": "store", "966": "woo-commerce", "1024": "woocommerce"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-58565": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-600x2900.jpg", "astra-page-api-url": "https://websitedemos.net/fashion-designer-boutique-08/wp-json/wp/v2/pages/1007", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/fashion-designer-boutique-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-58562": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-about-2.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-about-2-600x1400.jpg", "astra-page-api-url": "https://websitedemos.net/fashion-designer-boutique-08/wp-json/wp/v2/pages/1009", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fashion-designer-boutique-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-58563": {"title": "Collections", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-collection-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-collection-1-600x2040.jpg", "astra-page-api-url": "https://websitedemos.net/fashion-designer-boutique-08/wp-json/wp/v2/pages/1011", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/fashion-designer-boutique-08/collection/", "astra-sites-tag": {"596": "collection", "2453": "stock"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-58566": {"title": "New Arrivals", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-new-arrivals-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-new-arrivals-1-600x2023.jpg", "astra-page-api-url": "https://websitedemos.net/fashion-designer-boutique-08/wp-json/wp/v2/pages/1013", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/fashion-designer-boutique-08/new-arrivals/", "astra-sites-tag": {"2326": "new-arrival"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-58564": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-contact-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/10/fashion-designer-boutique-08-contact-1-600x1045.jpg", "astra-page-api-url": "https://websitedemos.net/fashion-designer-boutique-08/wp-json/wp/v2/pages/1015", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fashion-designer-boutique-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86286": {"title": "Fitness Trainer", "id": 86286, "publish-date": 1707743567, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/fitness-trainer-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/fitness-trainer-07-600x3726.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/fitness-trainer-07-400x2484.jpg", "astra-site-url": "//websitedemos.net/fitness-trainer-07", "astra-site-parent-id": 3485, "astra-sites-tag": {"845": "blue", "502": "exercise", "499": "fitness", "505": "fitness-trainer", "506": "functional-training", "500": "gym", "976": "gym-instructor", "504": "gym-trainer", "399": "one-page", "1020": "personal", "1844": "premium", "463": "single-page", "503": "trainer", "501": "work-out"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2787": "gym-fitness", "2771": "one-page", "2770": "personal"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2480": "fitness-wellness", "2927": "one-page"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86287": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/fitness-trainer-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/fitness-trainer-07-600x3726.jpg", "astra-page-api-url": "https://websitedemos.net/fitness-trainer-07/wp-json/wp/v2/pages/2", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fitness-trainer-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86088": {"title": "Courier &amp; Delivery Services", "id": 86088, "publish-date": 1707735705, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-600x2060.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-400x1373.jpg", "astra-site-url": "//websitedemos.net/courier-07", "astra-site-parent-id": 3472, "astra-sites-tag": {"1498": "bearer", "1493": "carrier", "1494": "courier", "1497": "delivery", "1495": "express", "1500": "messager", "1496": "parcel"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2854": "courier", "2831": "service", "2806": "transportation"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2473": "transport"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86091": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-600x2060.jpg", "astra-page-api-url": "https://websitedemos.net/courier-07/wp-json/wp/v2/pages/432", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/courier-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86089": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-about-us-600x1400.jpg", "astra-page-api-url": "https://websitedemos.net/courier-07/wp-json/wp/v2/pages/434", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/courier-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86093": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-services-600x1670.jpg", "astra-page-api-url": "https://websitedemos.net/courier-07/wp-json/wp/v2/pages/433", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/courier-07/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86094": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-testimonials-600x1286.jpg", "astra-page-api-url": "https://websitedemos.net/courier-07/wp-json/wp/v2/pages/436", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/courier-07/testimonials/", "astra-sites-tag": {"433": "testimonials"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86092": {"title": "Prices", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-prices.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-prices-600x1210.jpg", "astra-page-api-url": "https://websitedemos.net/courier-07/wp-json/wp/v2/pages/435", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/courier-07/prices/", "astra-sites-tag": {"815": "price"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86090": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-contact-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/courier-07-contact-us-600x1229.jpg", "astra-page-api-url": "https://websitedemos.net/courier-07/wp-json/wp/v2/pages/437", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/courier-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-56593": {"title": "Meditation Courses", "id": 56593, "publish-date": 1632945042, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-600x2660.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-400x1773.jpg", "astra-site-url": "//websitedemos.net/learn-meditation-08", "astra-site-parent-id": 2242, "astra-sites-tag": {"462": "courses", "467": "learndash", "1850": "meditation", "975": "online-learning", "720": "tutorials"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2494": "elearning", "2480": "fitness-wellness"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sfwd-lms", "init": "sfwd-lms/sfwd_lms.php", "name": "LearnDash LMS"}, {"slug": "learndash-course-grid", "init": "learndash-course-grid/learndash_course_grid.php", "name": "LearnDash LMS - Course Grid"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-56597": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-600x2660.jpg", "astra-page-api-url": "https://websitedemos.net/learn-meditation-08/wp-json/wp/v2/pages/572", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/learn-meditation-08/", "astra-sites-tag": {"814": "homepage"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56594": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-about-600x1259.jpg", "astra-page-api-url": "https://websitedemos.net/learn-meditation-08/wp-json/wp/v2/pages/612", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/learn-meditation-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56595": {"title": "Classes", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-classes.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-classes-600x976.jpg", "astra-page-api-url": "https://websitedemos.net/learn-meditation-08/wp-json/wp/v2/pages/607", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/learn-meditation-08/classes/", "astra-sites-tag": {"557": "classes"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "sfwd-lms", "init": "sfwd-lms/sfwd_lms.php", "name": "LearnDash LMS"}, {"slug": "learndash-course-grid", "init": "learndash-course-grid/learndash_course_grid.php", "name": "LearnDash LMS - Course Grid"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56596": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/09/learn-meditation-08-contact-600x855.jpg", "astra-page-api-url": "https://websitedemos.net/learn-meditation-08/wp-json/wp/v2/pages/617", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/learn-meditation-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "wpforms-lite", "init": "wpforms-lite/wpforms.php", "name": "WPForms Lite"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86341": {"title": "Christmas Party", "id": 86341, "publish-date": 1708706637, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-600x2427.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-400x1618.jpg", "astra-site-url": "//websitedemos.net/christmas-party-07", "astra-site-parent-id": 3491, "astra-sites-tag": {"3021": "christmas", "3029": "christmas<PERSON>er", "3024": "christmasfun", "3025": "christmas<PERSON><PERSON>", "3022": "christmasparty", "3032": "christmaspartyhopping", "3026": "christ<PERSON><PERSON><PERSON>", "3023": "christmastime", "3030": "christmasvibes", "3028": "chrstmasparty", "3027": "dance", "406": "dj", "882": "drinks", "3031": "holidayparty", "407": "music"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2869": "dj", "2784": "event", "2781": "music", "2868": "musician-singer"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"3430": "dark", "2489": "event-invitation"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86343": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-600x2427.jpg", "astra-page-api-url": "https://websitedemos.net/christmas-party-07/wp-json/wp/v2/pages/2106", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/christmas-party-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-86345": {"title": "Tickets", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-ticket.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-ticket-600x1059.jpg", "astra-page-api-url": "https://websitedemos.net/christmas-party-07/wp-json/wp/v2/pages/2107", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/christmas-party-07/tickets/", "astra-sites-tag": {"2954": "tickets"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-86344": {"title": "Program", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-program.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-program-600x1745.jpg", "astra-page-api-url": "https://websitedemos.net/christmas-party-07/wp-json/wp/v2/pages/2108", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/christmas-party-07/program/", "astra-sites-tag": {"2684": "program"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-86342": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/christmass-party-07-contact-600x728.jpg", "astra-page-api-url": "https://websitedemos.net/christmas-party-07/wp-json/wp/v2/pages/2109", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/christmas-party-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-47984": {"title": "LearnDash Academy", "id": 47984, "publish-date": 1603828903, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-600x1847.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-400x1231.jpg", "astra-site-url": "//websitedemos.net/learndash-academy-08", "astra-site-parent-id": 1691, "astra-sites-tag": {"462": "courses", "468": "elearning", "467": "learndash", "460": "learning", "466": "online-courses"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2494": "elearning"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sfwd-lms", "init": "sfwd-lms/sfwd_lms.php", "name": "LearnDash LMS"}, {"slug": "learndash-course-grid", "init": "learndash-course-grid/learndash_course_grid.php", "name": "LearnDash LMS - Course Grid"}, {"slug": "presto-player", "init": "presto-player/presto-player.php", "name": "Presto Player"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-47988": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-600x1847.jpg", "astra-page-api-url": "https://websitedemos.net/learndash-academy-08/wp-json/wp/v2/pages/25407", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/learndash-academy-08/", "astra-sites-tag": {"423": "home", "814": "homepage"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "sfwd-lms", "init": "sfwd-lms/sfwd_lms.php", "name": "LearnDash LMS"}, {"slug": "learndash-course-grid", "init": "learndash-course-grid/learndash_course_grid.php", "name": "LearnDash LMS - Course Grid"}, {"slug": "presto-player", "init": "presto-player/presto-player.php", "name": "Presto Player"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47986": {"title": "All Courses", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-all-courses.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-all-courses-600x966.jpg", "astra-page-api-url": "https://websitedemos.net/learndash-academy-08/wp-json/wp/v2/pages/25403", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/learndash-academy-08/all-courses/", "astra-sites-tag": {"1692": "all-courses", "462": "courses"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "sfwd-lms", "init": "sfwd-lms/sfwd_lms.php", "name": "LearnDash LMS"}, {"slug": "learndash-course-grid", "init": "learndash-course-grid/learndash_course_grid.php", "name": "LearnDash LMS - Course Grid"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47985": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-about-2.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-about-2-600x1582.jpg", "astra-page-api-url": "https://websitedemos.net/learndash-academy-08/wp-json/wp/v2/pages/25401", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/learndash-academy-08/about/", "astra-sites-tag": {"352": "about", "455": "about-us"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47987": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-contact-2.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/learndash-academy-08-contact-2-600x1274.jpg", "astra-page-api-url": "https://websitedemos.net/learndash-academy-08/wp-json/wp/v2/pages/25405", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/learndash-academy-08/contact/", "astra-sites-tag": {"415": "contact", "454": "contact-us"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86333": {"title": "Martial Arts School", "id": 86333, "publish-date": 1708616948, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-600x2920.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-400x1947.jpg", "astra-site-url": "//websitedemos.net/martial-arts-07", "astra-site-parent-id": 3490, "astra-sites-tag": {"3420": "black", "3419": "dark", "1804": "karate", "1803": "kung-fu", "1801": "martial-arts", "1805": "mixed-martial-arts", "3418": "mobile-friendly", "1844": "premium", "3417": "responsive", "1802": "self-defence"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2825": "coaching", "2772": "education", "2782": "school", "2873": "tutor"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": [], "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86336": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-600x2920.jpg", "astra-page-api-url": "https://websitedemos.net/martial-arts-07/wp-json/wp/v2/pages/247", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/martial-arts-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86334": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-about-us-600x2342.jpg", "astra-page-api-url": "https://websitedemos.net/martial-arts-07/wp-json/wp/v2/pages/248", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/martial-arts-07/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86337": {"title": "Programs", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-programs.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-programs-600x1810.jpg", "astra-page-api-url": "https://websitedemos.net/martial-arts-07/wp-json/wp/v2/pages/249", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/martial-arts-07/programs/", "astra-sites-tag": {"989": "programs"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86338": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-testimonials-600x1408.jpg", "astra-page-api-url": "https://websitedemos.net/martial-arts-07/wp-json/wp/v2/pages/250", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/martial-arts-07/testimonials/", "astra-sites-tag": {"433": "testimonials"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86335": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-contact-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/martial-arts-07-contact-us-600x1218.jpg", "astra-page-api-url": "https://websitedemos.net/martial-arts-07/wp-json/wp/v2/pages/251", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/martial-arts-07/contact-us/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-47932": {"title": "Online Health Coach", "id": 47932, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-600x1990.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-400x1327.jpg", "astra-site-url": "//websitedemos.net/online-health-coach-08", "astra-site-parent-id": 1684, "astra-sites-tag": {"462": "courses", "469": "ecourse", "468": "elearning", "518": "health-coach", "467": "learndash", "460": "learning", "466": "online-courses", "975": "online-learning"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2494": "elearning"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sfwd-lms", "init": "sfwd-lms/sfwd_lms.php", "name": "LearnDash LMS"}, {"slug": "learndash-course-grid", "init": "learndash-course-grid/learndash_course_grid.php", "name": "LearnDash LMS - Course Grid"}, {"slug": "presto-player", "init": "presto-player/presto-player.php", "name": "Presto Player"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-47941": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-600x1990.jpg", "astra-page-api-url": "https://websitedemos.net/online-health-coach-08/wp-json/wp/v2/pages/24365", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/online-health-coach-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47939": {"title": "All Courses", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-courses.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-courses-600x954.jpg", "astra-page-api-url": "https://websitedemos.net/online-health-coach-08/wp-json/wp/v2/pages/24366", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/online-health-coach-08/all-courses/", "astra-sites-tag": {"462": "courses"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "sfwd-lms", "init": "sfwd-lms/sfwd_lms.php", "name": "LearnDash LMS"}, {"slug": "learndash-course-grid", "init": "learndash-course-grid/learndash_course_grid.php", "name": "LearnDash LMS - Course Grid"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47938": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-about-600x1541.jpg", "astra-page-api-url": "https://websitedemos.net/online-health-coach-08/wp-json/wp/v2/pages/24368", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/online-health-coach-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47940": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/online-health-08-contact-600x734.jpg", "astra-page-api-url": "https://websitedemos.net/online-health-coach-08/wp-json/wp/v2/pages/24369", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/online-health-coach-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86325": {"title": "University", "id": 86325, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-600x2465.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-400x1643.jpg", "astra-site-url": "//websitedemos.net/university-07", "astra-site-parent-id": 3489, "astra-sites-tag": {"457": "college", "459": "education", "460": "learning", "1844": "premium", "456": "school", "458": "university"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2840": "college-university", "2772": "education", "2782": "school"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2491": "educational-institutes"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86330": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-600x2465.jpg", "astra-page-api-url": "https://websitedemos.net/university-07/wp-json/wp/v2/pages/503", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/university-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86326": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-about-600x2351.jpg", "astra-page-api-url": "https://websitedemos.net/university-07/wp-json/wp/v2/pages/506", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/university-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86328": {"title": "Courses", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-course.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-course-600x1703.jpg", "astra-page-api-url": "https://websitedemos.net/university-07/wp-json/wp/v2/pages/509", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/university-07/courses/", "astra-sites-tag": {"462": "courses"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86331": {"title": "Teachers", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-teachers.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-teachers-600x1262.jpg", "astra-page-api-url": "https://websitedemos.net/university-07/wp-json/wp/v2/pages/511", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/university-07/teachers/", "astra-sites-tag": {"2750": "teachers"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86329": {"title": "Gallery", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-gallery.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-gallery-600x1407.jpg", "astra-page-api-url": "https://websitedemos.net/university-07/wp-json/wp/v2/pages/513", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/university-07/gallery/", "astra-sites-tag": {"461": "gallery"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86327": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/university-07-contact-600x1090.jpg", "astra-page-api-url": "https://websitedemos.net/university-07/wp-json/wp/v2/pages/515", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/university-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48431": {"title": "Simply Natural", "id": 48431, "publish-date": 1603963839, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-600x2263.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-400x1508.jpg", "astra-site-url": "//websitedemos.net/plant-store-08", "astra-site-parent-id": 1732, "astra-sites-tag": {"2418": "commerce", "1017": "e-commerce", "496": "ecommerce", "2419": "eshopping", "535": "nature", "2422": "online-market", "2420": "online-shopping", "383": "online-store", "497": "plant-store", "495": "plants", "2421": "purchases", "2417": "reseller", "733": "shop", "2416": "shopping", "749": "store", "1024": "woocommerce"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2500": "multipurpose"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-68346": {"title": "Store", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-store.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-store-600x1973.jpg", "astra-page-api-url": "https://websitedemos.net/plant-store-08/wp-json/wp/v2/pages/118", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plant-store-08/store/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48432": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-about-us-600x1926.jpg", "astra-page-api-url": "https://websitedemos.net/plant-store-08/wp-json/wp/v2/pages/119", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plant-store-08/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48433": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-contact-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-contact-us-600x886.jpg", "astra-page-api-url": "https://websitedemos.net/plant-store-08/wp-json/wp/v2/pages/120", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plant-store-08/contact-us/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-48434": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/plant-store-08-600x2263.jpg", "astra-page-api-url": "https://websitedemos.net/plant-store-08/wp-json/wp/v2/pages/15", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/plant-store-08/", "astra-sites-tag": {"814": "homepage"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": 75439, "ecommerce_parent_template": 75439}, "id-75439": {"title": "Simply Natural", "id": 75439, "publish-date": 1691769102, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08-600x2204.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08-400x1469.jpg", "astra-site-url": "//websitedemos.net/plants-stores-08", "astra-site-parent-id": 3372, "astra-sites-tag": {"2419": "eshopping", "2422": "online-market", "2390": "online-plant-shop", "2420": "online-shopping", "383": "online-store", "1414": "plant-shop", "497": "plant-store", "495": "plants", "2421": "purchases", "2417": "reseller", "733": "shop", "2416": "shopping", "749": "store", "3550": "<PERSON><PERSON>t"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": {"2769": "ecommerce", "2857": "online-shopping"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": [], "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-75442": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08-600x2204.jpg", "astra-page-api-url": "https://websitedemos.net/plants-stores-08/wp-json/wp/v2/pages/605", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/plants-stores-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75440": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08-about-600x1956.jpg", "astra-page-api-url": "https://websitedemos.net/plants-stores-08/wp-json/wp/v2/pages/611", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plants-stores-08/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-75499": {"title": "Shop", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/plants-stores-08/wp-json/wp/v2/pages/285", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plants-stores-08/shop/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75497": {"title": "Checkout", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/plants-stores-08/wp-json/wp/v2/pages/282", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plants-stores-08/checkout/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75498": {"title": "Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/plants-stores-08/wp-json/wp/v2/pages/283", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plants-stores-08/customer-dashboard/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75441": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/plants-stores-08-contact-600x921.jpg", "astra-page-api-url": "https://websitedemos.net/plants-stores-08/wp-json/wp/v2/pages/612", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/plants-stores-08/contact-us/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": 48431, "ecommerce_parent_template": ""}, "id-86304": {"title": "Transport Services", "id": 86304, "publish-date": 1707499299, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/transport-services-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/transport-services-07-600x2842.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/transport-services-07-400x1895.jpg", "astra-site-url": "//websitedemos.net/transport-services-07", "astra-site-parent-id": 3486, "astra-sites-tag": {"394": "air-cargo", "845": "blue", "391": "cargo-services", "392": "freight-services", "388": "good-carrier", "389": "logistics", "399": "one-page", "846": "red", "393": "road-transport", "390": "shipping", "366": "transport", "387": "trucking"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2771": "one-page", "2831": "service", "2806": "transportation"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"3430": "dark", "2927": "one-page", "2473": "transport"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86305": {"title": "One Page Website", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/transport-services-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/transport-services-07-600x2842.jpg", "astra-page-api-url": "https://websitedemos.net/transport-services-07/wp-json/wp/v2/pages/297", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/transport-services-07/", "astra-sites-tag": {"850": "landing-page"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-59540": {"title": "Library Cafe", "id": 59540, "publish-date": 1646329876, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-600x2416.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-400x1610.jpg", "astra-site-url": "//websitedemos.net/cafe-library-08", "astra-site-parent-id": 2636, "astra-sites-tag": {"2607": "book-house", "1740": "books", "2606": "books-room", "1527": "cafeteria", "2603": "coffee-bar", "2604": "library", "2605": "library-cafe"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": {"2804": "cafe-bakery", "2785": "food", "2776": "restaurant"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2496": "restaurant-food"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-59543": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-600x2416.jpg", "astra-page-api-url": "https://websitedemos.net/cafe-library-08/wp-json/wp/v2/pages/704", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cafe-library-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59541": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-about-600x1438.jpg", "astra-page-api-url": "https://websitedemos.net/cafe-library-08/wp-json/wp/v2/pages/710", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cafe-library-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59544": {"title": "<PERSON><PERSON>", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-menu.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-menu-600x1081.jpg", "astra-page-api-url": "https://websitedemos.net/cafe-library-08/wp-json/wp/v2/pages/706", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cafe-library-08/menu/", "astra-sites-tag": {"565": "menu"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59545": {"title": "Reviews", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-reviews.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-reviews-600x1040.jpg", "astra-page-api-url": "https://websitedemos.net/cafe-library-08/wp-json/wp/v2/pages/708", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cafe-library-08/reviews/", "astra-sites-tag": {"432": "review"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-59542": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2022/03/cafe-library-08-contact-600x939.jpg", "astra-page-api-url": "https://websitedemos.net/cafe-library-08/wp-json/wp/v2/pages/712", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cafe-library-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-86059": {"title": "Tanz Tech", "id": 86059, "publish-date": 1707493975, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-600x3455.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-400x2303.jpg", "astra-site-url": "//websitedemos.net/saas-07", "astra-site-parent-id": 3469, "astra-sites-tag": {"2712": "analytics", "3131": "automation", "692": "business", "3132": "business-operations", "2708": "saas", "3130": "tech", "3129": "technology"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2831": "service", "2817": "technology"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2487": "agency", "2499": "technology"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-86063": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-600x3455.jpg", "astra-page-api-url": "https://websitedemos.net/saas-07/wp-json/wp/v2/pages/761", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/saas-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86060": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-about-600x2361.jpg", "astra-page-api-url": "https://websitedemos.net/saas-07/wp-json/wp/v2/pages/766", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/saas-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86062": {"title": "Features", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-features.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-features-600x2288.jpg", "astra-page-api-url": "https://websitedemos.net/saas-07/wp-json/wp/v2/pages/764", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/saas-07/features/", "astra-sites-tag": {"453": "features"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86064": {"title": "Pricing", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-pricing.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-pricing-600x1451.jpg", "astra-page-api-url": "https://websitedemos.net/saas-07/wp-json/wp/v2/pages/768", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/saas-07/pricing/", "astra-sites-tag": {"585": "pricing"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-86061": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/02/saas-07-contact-600x1070.jpg", "astra-page-api-url": "https://websitedemos.net/saas-07/wp-json/wp/v2/pages/770", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/saas-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}}