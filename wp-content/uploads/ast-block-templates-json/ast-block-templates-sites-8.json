{"id-85763": {"title": "AppPlus", "id": 85763, "publish-date": 1706628678, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/app-landing-page-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/app-landing-page-07-600x3370.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/app-landing-page-07-400x2247.jpg", "astra-site-url": "//websitedemos.net/app-landing-page-07", "astra-site-parent-id": 3452, "astra-sites-tag": {"1583": "app-landing-page", "423": "home", "850": "landing-page", "629": "product-landing-page"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2863": "landing-page", "2881": "mobile-app", "2771": "one-page"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2927": "one-page", "2499": "technology"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85764": {"title": "<PERSON>", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/app-landing-page-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/app-landing-page-07-600x3370.jpg", "astra-page-api-url": "https://websitedemos.net/app-landing-page-07/wp-json/wp/v2/pages/268", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/app-landing-page-07/", "astra-sites-tag": {"850": "landing-page"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48318": {"title": "Nutritionist", "id": 48318, "publish-date": 1603908961, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-600x2730.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-400x1820.jpg", "astra-site-url": "//websitedemos.net/nutritionist-08", "astra-site-parent-id": 1722, "astra-sites-tag": {"753": "dietitian", "1726": "nutrition-coach", "1727": "nutrition-coaching", "1725": "nutrition-trainer", "754": "nutritionist", "1454": "personal-trainer"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2480": "fitness-wellness"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-69529": {"title": "Blog", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-blog.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-blog-600x1061.jpg", "astra-page-api-url": "https://websitedemos.net/nutritionist-08/wp-json/wp/v2/pages/728", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/nutritionist-08/blog/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48321": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-600x2730.jpg", "astra-page-api-url": "https://websitedemos.net/nutritionist-08/wp-json/wp/v2/pages/6", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/nutritionist-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48319": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-about-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-about-1-600x1230.jpg", "astra-page-api-url": "https://websitedemos.net/nutritionist-08/wp-json/wp/v2/pages/134", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/nutritionist-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48322": {"title": "Programs", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-programs.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-programs-600x1480.jpg", "astra-page-api-url": "https://websitedemos.net/nutritionist-08/wp-json/wp/v2/pages/164", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/nutritionist-08/programs/", "astra-sites-tag": {"989": "programs"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48323": {"title": "Success Stories", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-success-stories-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-success-stories-1-600x942.jpg", "astra-page-api-url": "https://websitedemos.net/nutritionist-08/wp-json/wp/v2/pages/180", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/nutritionist-08/success-stories/", "astra-sites-tag": {"432": "review", "990": "success-stories", "433": "testimonials"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48320": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-contact-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/nutritionist-08-contact-1-600x808.jpg", "astra-page-api-url": "https://websitedemos.net/nutritionist-08/wp-json/wp/v2/pages/196", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/nutritionist-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85621": {"title": "Coffee Shop", "id": 85621, "publish-date": 1706626568, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-600x2281.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-400x1520.jpg", "astra-site-url": "//websitedemos.net/coffee-shop-07", "astra-site-parent-id": 3441, "astra-sites-tag": {"693": "cafe", "2042": "cafe-bar", "1527": "cafeteria", "2043": "coffee-house", "1575": "coffee-shop", "399": "one-page", "2421": "purchases", "2417": "reseller", "335": "restaurant", "2041": "snack-bar"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2785": "food", "2776": "restaurant"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2496": "restaurant-food"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85625": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-600x2281.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-shop-07/wp-json/wp/v2/pages/597", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-shop-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85622": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-about-600x1362.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-shop-07/wp-json/wp/v2/pages/599", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-shop-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85626": {"title": "Our Menu", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-our-menu.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-our-menu-600x1167.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-shop-07/wp-json/wp/v2/pages/598", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-shop-07/our-menu/", "astra-sites-tag": {"1578": "our-menu"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85624": {"title": "Gallery", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-gallery.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-gallery-600x987.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-shop-07/wp-json/wp/v2/pages/600", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-shop-07/gallery/", "astra-sites-tag": {"461": "gallery"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85623": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/coffee-shop-07-contact-600x819.jpg", "astra-page-api-url": "https://websitedemos.net/coffee-shop-07/wp-json/wp/v2/pages/601", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/coffee-shop-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48631": {"title": "Yoga Instructor", "id": 48631, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-600x2239.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-400x1493.jpg", "astra-site-url": "//websitedemos.net/yoga-instructor-08", "astra-site-parent-id": 1753, "astra-sites-tag": {"499": "fitness", "976": "gym-instructor", "849": "health-yoga", "1020": "personal", "395": "yoga-instructor", "397": "yoga-studio", "398": "yoga-trainer"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": {"2787": "gym-fitness", "2770": "personal", "2808": "yoga"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2480": "fitness-wellness"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-48635": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-600x2239.jpg", "astra-page-api-url": "https://websitedemos.net/yoga-instructor-08/wp-json/wp/v2/pages/51", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/yoga-instructor-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48632": {"title": "About Me", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-about-me-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-about-me-1-600x1297.jpg", "astra-page-api-url": "https://websitedemos.net/yoga-instructor-08/wp-json/wp/v2/pages/52", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/yoga-instructor-08/about-me/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48633": {"title": "Classes", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-classes-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-classes-1-600x1746.jpg", "astra-page-api-url": "https://websitedemos.net/yoga-instructor-08/wp-json/wp/v2/pages/53", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/yoga-instructor-08/classes/", "astra-sites-tag": {"557": "classes"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48636": {"title": "Publications", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-publications-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-publications-1-600x1082.jpg", "astra-page-api-url": "https://websitedemos.net/yoga-instructor-08/wp-json/wp/v2/pages/54", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/yoga-instructor-08/publications/", "astra-sites-tag": {"558": "publication"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48634": {"title": "Get Fit", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-get-fit-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/yoga-instructor-08-get-fit-1-600x1079.jpg", "astra-page-api-url": "https://websitedemos.net/yoga-instructor-08/wp-json/wp/v2/pages/55", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/yoga-instructor-08/get-fit/", "astra-sites-tag": {"415": "contact", "377": "get-in-touch"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85733": {"title": "Plant Shop", "id": 85733, "publish-date": 1706554719, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/plant-shop-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/plant-shop-07-600x2615.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/plant-shop-07-400x1743.jpg", "astra-site-url": "//websitedemos.net/plant-shop-07", "astra-site-parent-id": 3448, "astra-sites-tag": {"2418": "commerce", "1017": "e-commerce", "496": "ecommerce", "2419": "eshopping", "2422": "online-market", "2420": "online-shopping", "383": "online-store", "497": "plant-store", "495": "plants", "2421": "purchases", "2417": "reseller", "733": "shop", "2416": "shopping", "749": "store", "1024": "woocommerce"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business", "38": "ecommerce"}, "astra-sites-template-category": {"2777": "business", "2769": "ecommerce", "2830": "nature", "2857": "online-shopping"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce", "2476": "gardening"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85734": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/plant-shop-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/plant-shop-07-600x2615.jpg", "astra-page-api-url": "https://websitedemos.net/plant-shop-07/wp-json/wp/v2/pages/884", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/plant-shop-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-49936": {"title": "Recipe Blog", "id": 49936, "publish-date": 1614595588, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-1-600x1623.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-1-400x1082.jpg", "astra-site-url": "//websitedemos.net/recipe-blog-08", "astra-site-parent-id": 1858, "astra-sites-tag": {"792": "cooking", "787": "dinner", "336": "food", "793": "food-making", "788": "snack"}, "astra-sites-type": "free", "astra-site-category": {"37": "blog", "39": "free"}, "astra-sites-template-category": {"2828": "blog", "2785": "food", "2770": "personal", "2776": "restaurant"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2497": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-49940": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-1-600x1623.jpg", "astra-page-api-url": "https://websitedemos.net/recipe-blog-08/wp-json/wp/v2/pages/1055", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/recipe-blog-08/", "astra-sites-tag": {"814": "homepage"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-49937": {"title": "About Me", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-about-me-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-about-me-1-600x897.jpg", "astra-page-api-url": "https://websitedemos.net/recipe-blog-08/wp-json/wp/v2/pages/1071", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/recipe-blog-08/about-me/", "astra-sites-tag": {"1007": "about-me"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-49938": {"title": "Blog", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-blog-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-blog-1-600x1429.jpg", "astra-page-api-url": "https://websitedemos.net/recipe-blog-08/wp-json/wp/v2/pages/1067", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/recipe-blog-08/blog/", "astra-sites-tag": {"480": "blog"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-49939": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-contact-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/recipe-blog-08-contact-1-600x758.jpg", "astra-page-api-url": "https://websitedemos.net/recipe-blog-08/wp-json/wp/v2/pages/1074", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/recipe-blog-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85518": {"title": "Shoe Store", "id": 85518, "publish-date": 1706550951, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07-600x1814.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07-400x1209.jpg", "astra-site-url": "//websitedemos.net/shoe-store-07", "astra-site-parent-id": 3436, "astra-sites-tag": {"2418": "commerce", "1017": "e-commerce", "496": "ecommerce", "1817": "ecommerce-store", "2419": "eshopping", "2422": "online-market", "732": "online-shop", "2420": "online-shopping", "383": "online-store", "2421": "purchases", "2417": "reseller", "384": "running-shoes", "382": "shoe-store", "385": "shoes", "733": "shop", "2416": "shopping", "2427": "sports-shoes", "749": "store", "966": "woo-commerce", "1024": "woocommerce"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business", "38": "ecommerce"}, "astra-sites-template-category": {"2769": "ecommerce", "2857": "online-shopping", "2878": "shoes"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-gateway-paypal-express-checkout", "init": "woocommerce-gateway-paypal-express-checkout/woocommerce-gateway-paypal-express-checkout.php", "name": "WooCommerce PayPal Checkout Gateway"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85521": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07-600x1814.jpg", "astra-page-api-url": "https://websitedemos.net/shoe-store-07/wp-json/wp/v2/pages/1041", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/shoe-store-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-gateway-paypal-express-checkout", "init": "woocommerce-gateway-paypal-express-checkout/woocommerce-gateway-paypal-express-checkout.php", "name": "WooCommerce PayPal Checkout Gateway"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-85519": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07-about-600x1628.jpg", "astra-page-api-url": "https://websitedemos.net/shoe-store-07/wp-json/wp/v2/pages/1044", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/shoe-store-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85520": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/shoe-store-07-contact-600x1336.jpg", "astra-page-api-url": "https://websitedemos.net/shoe-store-07/wp-json/wp/v2/pages/1046", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/shoe-store-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48076": {"title": "Personal Fitness Trainer", "id": 48076, "publish-date": 1603893488, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/fitness-trainer-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/fitness-trainer-08-600x3142.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/fitness-trainer-08-400x2094.jpg", "astra-site-url": "//websitedemos.net/fitness-trainer-08", "astra-site-parent-id": 1697, "astra-sites-tag": {"502": "exercise", "499": "fitness", "506": "functional-training", "500": "gym", "504": "gym-trainer", "399": "one-page", "463": "single-page", "503": "trainer", "501": "work-out"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2480": "fitness-wellness"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-48077": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/fitness-trainer-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/fitness-trainer-08-600x3142.jpg", "astra-page-api-url": "https://websitedemos.net/fitness-trainer-08/wp-json/wp/v2/pages/10", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fitness-trainer-08/", "astra-sites-tag": {"423": "home", "814": "homepage", "463": "single-page"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85590": {"title": "Brandstore Pro", "id": 85590, "publish-date": 1706532719, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-600x2027.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-400x1351.jpg", "astra-site-url": "//websitedemos.net/store-07", "astra-site-parent-id": 3440, "astra-sites-tag": {"639": "accessories", "638": "brand-store", "880": "cartflows", "640": "cloth-store", "637": "clothing", "2418": "commerce", "1017": "e-commerce", "496": "ecommerce", "2422": "online-market", "732": "online-shop", "2420": "online-shopping", "383": "online-store", "967": "product-store", "2421": "purchases", "2417": "reseller", "733": "shop", "2416": "shopping", "749": "store", "966": "woo-commerce", "1024": "woocommerce"}, "astra-sites-type": "agency-mini", "astra-site-category": {"38": "ecommerce"}, "astra-sites-template-category": {"2777": "business", "2769": "ecommerce", "2858": "fashion-clothing", "2857": "online-shopping"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2479": "beauty", "2495": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-gateway-paypal-express-checkout", "init": "woocommerce-gateway-paypal-express-checkout/woocommerce-gateway-paypal-express-checkout.php", "name": "WooCommerce PayPal Checkout Gateway"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85593": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-600x2027.jpg", "astra-page-api-url": "https://websitedemos.net/store-07/wp-json/wp/v2/pages/1314", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/store-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-gateway-paypal-express-checkout", "init": "woocommerce-gateway-paypal-express-checkout/woocommerce-gateway-paypal-express-checkout.php", "name": "WooCommerce PayPal Checkout Gateway"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-85591": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-about-us-600x1358.jpg", "astra-page-api-url": "https://websitedemos.net/store-07/wp-json/wp/v2/pages/1317", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/store-07/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85594": {"title": "Lookbook", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-lookbook.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-lookbook-600x1583.jpg", "astra-page-api-url": "https://websitedemos.net/store-07/wp-json/wp/v2/pages/1322", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/store-07/lookbook/", "astra-sites-tag": {"752": "lookbook"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85592": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-contact-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/store-07-contact-us-600x1534.jpg", "astra-page-api-url": "https://websitedemos.net/store-07/wp-json/wp/v2/pages/1319", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/store-07/contact-us/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce"}, "required-plugins": [{"slug": "astra-addon", "init": "astra-addon/astra-addon.php", "name": "Astra Pro"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-gateway-paypal-express-checkout", "init": "woocommerce-gateway-paypal-express-checkout/woocommerce-gateway-paypal-express-checkout.php", "name": "WooCommerce PayPal Checkout Gateway"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-75418": {"title": "Cosmetics Store", "id": 75418, "publish-date": 1690994681, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-600x2228.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-400x1485.jpg", "astra-site-url": "//websitedemos.net/cosmetic-store-08", "astra-site-parent-id": 3371, "astra-sites-tag": {"1465": "beauty-products", "1466": "cosmetic-products", "1464": "cosmetic-store", "1463": "cosmetics", "496": "ecommerce"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": {"2790": "beauty-fashion", "2777": "business", "2769": "ecommerce", "2890": "makeup-cosmetic"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-75421": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-600x2228.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetic-store-08/wp-json/wp/v2/pages/1147", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/cosmetic-store-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-75419": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-about-600x1259.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetic-store-08/wp-json/wp/v2/pages/1160", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetic-store-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-75502": {"title": "Shop", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/cosmetic-store-08/wp-json/wp/v2/pages/287", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetic-store-08/shop/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75500": {"title": "Checkout", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/cosmetic-store-08/wp-json/wp/v2/pages/284", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetic-store-08/checkout/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75501": {"title": "Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/cosmetic-store-08/wp-json/wp/v2/pages/285", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetic-store-08/customer-dashboard/", "astra-sites-tag": [], "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "<PERSON><PERSON>t", "init": "surecart/surecart.php", "name": "SureCart"}]}, "id-75422": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-testimonials-600x1191.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetic-store-08/wp-json/wp/v2/pages/1164", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetic-store-08/testimonials/", "astra-sites-tag": {"433": "testimonials"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-75420": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2023/08/cosmetic-store-08-contact-600x1200.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetic-store-08/wp-json/wp/v2/pages/1162", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetic-store-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": 48591, "ecommerce_parent_template": ""}, "id-48591": {"title": "Cosmetics Store", "id": 48591, "publish-date": 1603984615, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-600x2401.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-400x1601.jpg", "astra-site-url": "//websitedemos.net/cosmetics-store-08", "astra-site-parent-id": 1748, "astra-sites-tag": {"1023": "beauty", "1465": "beauty-products", "1466": "cosmetic-products", "1464": "cosmetic-store", "1463": "cosmetics", "496": "ecommerce", "1024": "woocommerce"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-48594": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-600x2401.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetics-store-08/wp-json/wp/v2/pages/542", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/cosmetics-store-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-48592": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-about-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-about-1-600x1218.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetics-store-08/wp-json/wp/v2/pages/545", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetics-store-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48595": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-testimonials-600x1164.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetics-store-08/wp-json/wp/v2/pages/544", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetics-store-08/testimonials/", "astra-sites-tag": {"433": "testimonials"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48593": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-contact-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/cosmetics-store-08-contact-1-600x1187.jpg", "astra-page-api-url": "https://websitedemos.net/cosmetics-store-08/wp-json/wp/v2/pages/546", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/cosmetics-store-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": 75418, "ecommerce_parent_template": 75418}, "id-85545": {"title": "Business Magazine", "id": 85545, "publish-date": 1706299881, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07-600x3468.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07-400x2312.jpg", "astra-site-url": "//websitedemos.net/business-magazine-07", "astra-site-parent-id": 3439, "astra-sites-tag": {"480": "blog", "2275": "business-blog", "2277": "business-magazine", "2276": "business-news", "2273": "editorial", "2274": "magazine", "2271": "news", "2272": "publishing", "2278": "publishing-house"}, "astra-sites-type": "agency-mini", "astra-site-category": {"37": "blog"}, "astra-sites-template-category": {"2828": "blog", "2774": "magazine", "2859": "news"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2497": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85548": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07-600x3468.jpg", "astra-page-api-url": "https://websitedemos.net/business-magazine-07/wp-json/wp/v2/pages/891", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/business-magazine-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-85546": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07-about-600x1564.jpg", "astra-page-api-url": "https://websitedemos.net/business-magazine-07/wp-json/wp/v2/pages/892", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/business-magazine-07/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-85547": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/business-magazine-07-contact-600x950.jpg", "astra-page-api-url": "https://websitedemos.net/business-magazine-07/wp-json/wp/v2/pages/893", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/business-magazine-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-56682": {"title": "Chartered Accountant", "id": 56682, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-600x2501.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-400x1667.jpg", "astra-site-url": "//websitedemos.net/accountant-08", "astra-site-parent-id": 2252, "astra-sites-tag": {"447": "accountant", "449": "chartered-accountant", "2254": "finance-and-accounting"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2481": "finance-service"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-56685": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-600x2501.jpg", "astra-page-api-url": "https://websitedemos.net/accountant-08/wp-json/wp/v2/pages/6", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/accountant-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56683": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-about-600x1203.jpg", "astra-page-api-url": "https://websitedemos.net/accountant-08/wp-json/wp/v2/pages/7", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/accountant-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56686": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-services-600x1364.jpg", "astra-page-api-url": "https://websitedemos.net/accountant-08/wp-json/wp/v2/pages/8", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/accountant-08/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56688": {"title": "Why Choose Me", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-why-choose-me.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-why-choose-me-600x1040.jpg", "astra-page-api-url": "https://websitedemos.net/accountant-08/wp-json/wp/v2/pages/9", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/accountant-08/why-choose-me/", "astra-sites-tag": {"453": "features", "760": "why-choose-us", "852": "why-us"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56687": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-testimonials-600x1326.jpg", "astra-page-api-url": "https://websitedemos.net/accountant-08/wp-json/wp/v2/pages/10", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/accountant-08/testimonials/", "astra-sites-tag": {"433": "testimonials"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-56684": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/03/accountant-08-contact-600x975.jpg", "astra-page-api-url": "https://websitedemos.net/accountant-08/wp-json/wp/v2/pages/11", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/accountant-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85526": {"title": "Blockchain Technology", "id": 85526, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-600x2329.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-400x1553.jpg", "astra-site-url": "//websitedemos.net/blockchain-technology-07", "astra-site-parent-id": 3437, "astra-sites-tag": {"3097": "bitcoin", "3095": "blockchain", "3096": "blockchaintechnology", "3101": "blockchaintrends", "3098": "crypto", "3100": "cryptoart", "3102": "cryptoartist", "3099": "nfts"}, "astra-sites-type": "agency-mini", "astra-site-category": {"37": "blog", "27": "business"}, "astra-sites-template-category": {"2828": "blog", "2777": "business", "2817": "technology"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2497": "blog", "2499": "technology"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85529": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-600x2329.jpg", "astra-page-api-url": "https://websitedemos.net/blockchain-technology-07/wp-json/wp/v2/pages/564", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/blockchain-technology-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-85527": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-about-600x1287.jpg", "astra-page-api-url": "https://websitedemos.net/blockchain-technology-07/wp-json/wp/v2/pages/565", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/blockchain-technology-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85530": {"title": "Resources", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-resources.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-resources-600x1305.jpg", "astra-page-api-url": "https://websitedemos.net/blockchain-technology-07/wp-json/wp/v2/pages/567", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/blockchain-technology-07/resources/", "astra-sites-tag": {"3103": "resources"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85528": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/blockchain-technology-07-contact-600x763.jpg", "astra-page-api-url": "https://websitedemos.net/blockchain-technology-07/wp-json/wp/v2/pages/568", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/blockchain-technology-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48490": {"title": "Animal Welfare", "id": 48490, "publish-date": 1603984632, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-600x2724.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-400x1816.jpg", "astra-site-url": "//websitedemos.net/animal-welfare-08", "astra-site-parent-id": 1741, "astra-sites-tag": {"625": "charity", "735": "donate", "1013": "donation", "624": "foundation", "1482": "givewp", "745": "organization", "1012": "welfare"}, "astra-sites-type": "free", "astra-site-category": {"39": "free", "36": "other"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2492": "non-profit"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "give", "init": "give/give.php", "name": "Give - Donation Plugin"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-57933": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1485", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-57934": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1486", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard-2/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-57935": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1488", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard-3/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-57936": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1489", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard-3-5/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-57937": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1490", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard-3-4/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-57938": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1491", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard-3-3/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-57939": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1492", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard-3-2/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-57940": {"title": "Donor Dashboard", "featured-image-url": "", "thumbnail-image-url": "", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1493", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/donor-dashboard-4/", "astra-sites-tag": [], "site-pages-type": [], "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": [], "required-plugins": ""}, "id-48493": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-600x2724.jpg", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1026", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"61": "free", "139": "other"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48491": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-about-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-about-1-600x1695.jpg", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1027", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"61": "free", "139": "other"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48494": {"title": "See All Campaigns", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-see-all-campaigns.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-see-all-campaigns-600x1711.jpg", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1028", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/animal-welfare-08/see-all-campaigns/", "astra-sites-tag": {"1479": "campaigns"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"61": "free", "139": "other"}, "required-plugins": [{"slug": "give", "init": "give/give.php", "name": "Give - Donation Plugin"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48495": {"title": "Star Contributors", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-star-contributor.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-star-contributor-600x1625.jpg", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1029", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/animal-welfare-08/star-contributors/", "astra-sites-tag": {"1014": "contribute", "1481": "star-donors"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"61": "free", "139": "other"}, "required-plugins": [{"slug": "give", "init": "give/give.php", "name": "Give - Donation Plugin"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48492": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-contact-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/animal-welfare-08-contact-1-600x1230.jpg", "astra-page-api-url": "https://websitedemos.net/animal-welfare-08/wp-json/wp/v2/pages/1030", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/animal-welfare-08/contact/", "astra-sites-tag": {"415": "contact", "454": "contact-us"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"61": "free", "139": "other"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85448": {"title": "Fast Food Restaurant", "id": 85448, "publish-date": 1706269033, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-600x3669.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-400x2446.jpg", "astra-site-url": "//websitedemos.net/fast-food-07", "astra-site-parent-id": 3424, "astra-sites-tag": {"1996": "burger", "2011": "fast-food-chain", "2013": "fast-food-restaurant", "350": "hotel", "1844": "premium", "335": "restaurant", "2020": "restaurant-chain", "1993": "snack-food"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2785": "food", "2876": "food-ordering", "2776": "restaurant"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2496": "restaurant-food"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85452": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-600x3669.jpg", "astra-page-api-url": "https://websitedemos.net/fast-food-07/wp-json/wp/v2/pages/883", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fast-food-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85449": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-about-us-600x1994.jpg", "astra-page-api-url": "https://websitedemos.net/fast-food-07/wp-json/wp/v2/pages/886", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fast-food-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85451": {"title": "Franchisee", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-franchisee.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-franchisee-600x1639.jpg", "astra-page-api-url": "https://websitedemos.net/fast-food-07/wp-json/wp/v2/pages/887", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fast-food-07/franchisee/", "astra-sites-tag": {"1991": "franchisee"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85454": {"title": "Our Menu", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-our-menu.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-our-menu-600x2481.jpg", "astra-page-api-url": "https://websitedemos.net/fast-food-07/wp-json/wp/v2/pages/884", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fast-food-07/our-menu/", "astra-sites-tag": {"1578": "our-menu"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85453": {"title": "Offers", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-offers.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-offers-600x1587.jpg", "astra-page-api-url": "https://websitedemos.net/fast-food-07/wp-json/wp/v2/pages/885", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fast-food-07/offers/", "astra-sites-tag": {"1969": "offers"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85450": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/fast-food-07-contact-600x1334.jpg", "astra-page-api-url": "https://websitedemos.net/fast-food-07/wp-json/wp/v2/pages/888", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/fast-food-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48442": {"title": "Dentist Clinic", "id": 48442, "publish-date": 1603913248, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/dentist-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/dentist-08-1-600x2480.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/dentist-08-1-400x1653.jpg", "astra-site-url": "//websitedemos.net/dentist-08", "astra-site-parent-id": 1733, "astra-sites-tag": {"338": "dental", "623": "dentist", "1734": "dentist-clinic"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2490": "healthcare"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-48443": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/dentist-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/dentist-08-1-600x2480.jpg", "astra-page-api-url": "https://websitedemos.net/dentist-08/wp-json/wp/v2/pages/409", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/dentist-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85440": {"title": "Deeplight Restaurant", "id": 85440, "publish-date": 1706211394, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-600x3268.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-400x2178.jpg", "astra-site-url": "//websitedemos.net/restaurant-07", "astra-site-parent-id": 3423, "astra-sites-tag": {"562": "bistro", "972": "club", "560": "cuisine", "561": "eatery", "336": "food", "350": "hotel", "559": "outlet", "335": "restaurant", "970": "restro"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2785": "food", "2876": "food-ordering", "2776": "restaurant"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2496": "restaurant-food"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85444": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-600x3268.jpg", "astra-page-api-url": "https://websitedemos.net/restaurant-07/wp-json/wp/v2/pages/1240", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/restaurant-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85441": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-about-600x2026.jpg", "astra-page-api-url": "https://websitedemos.net/restaurant-07/wp-json/wp/v2/pages/1502", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/restaurant-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85445": {"title": "<PERSON><PERSON>", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-menu.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-menu-600x2368.jpg", "astra-page-api-url": "https://websitedemos.net/restaurant-07/wp-json/wp/v2/pages/1499", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/restaurant-07/menu/", "astra-sites-tag": {"565": "menu"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85443": {"title": "Gallery", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-gallery.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-gallery-600x1303.jpg", "astra-page-api-url": "https://websitedemos.net/restaurant-07/wp-json/wp/v2/pages/1505", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/restaurant-07/gallery/", "astra-sites-tag": {"461": "gallery"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85446": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-testimonials-600x2120.jpg", "astra-page-api-url": "https://websitedemos.net/restaurant-07/wp-json/wp/v2/pages/1508", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/restaurant-07/testimonials/", "astra-sites-tag": {"433": "testimonials"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85442": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/restaurant-07-contact-600x1786.jpg", "astra-page-api-url": "https://websitedemos.net/restaurant-07/wp-json/wp/v2/pages/1511", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/restaurant-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-47862": {"title": "Pet Services", "id": 47862, "publish-date": 1603821511, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-600x2415.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-400x1610.jpg", "astra-site-url": "//websitedemos.net/pet-sitting-08", "astra-site-parent-id": 1680, "astra-sites-tag": {"374": "pet-care", "372": "pet-minding", "371": "pet-sitting", "373": "pet-training", "1687": "pet-walking"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2472": "pets"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-47864": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-600x2415.jpg", "astra-page-api-url": "https://websitedemos.net/pet-sitting-08/wp-json/wp/v2/pages/506", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pet-sitting-08/", "astra-sites-tag": {"814": "homepage"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47866": {"title": "Who We Are", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-who-we-are.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-who-we-are-600x2193.jpg", "astra-page-api-url": "https://websitedemos.net/pet-sitting-08/wp-json/wp/v2/pages/508", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pet-sitting-08/who-we-are/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47865": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-services-600x2250.jpg", "astra-page-api-url": "https://websitedemos.net/pet-sitting-08/wp-json/wp/v2/pages/509", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pet-sitting-08/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-47863": {"title": "Get In Touch", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-get-in-touch.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/pet-sitting-08-get-in-touch-600x1036.jpg", "astra-page-api-url": "https://websitedemos.net/pet-sitting-08/wp-json/wp/v2/pages/510", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pet-sitting-08/get-in-touch/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85349": {"title": "Swimming Pool Services", "id": 85349, "publish-date": 1706206829, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-600x2714.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-400x1809.jpg", "astra-site-url": "//websitedemos.net/pool-services-07", "astra-site-parent-id": 3410, "astra-sites-tag": {"567": "pool-maintenance", "569": "swimming-pool", "570": "water-pool"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2819": "cleaning", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2471": "local-technician"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85354": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-600x2714.jpg", "astra-page-api-url": "https://websitedemos.net/pool-services-07/wp-json/wp/v2/pages/631", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pool-services-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85350": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-about-600x1478.jpg", "astra-page-api-url": "https://websitedemos.net/pool-services-07/wp-json/wp/v2/pages/633", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pool-services-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85356": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-services-600x1033.jpg", "astra-page-api-url": "https://websitedemos.net/pool-services-07/wp-json/wp/v2/pages/635", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pool-services-07/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85353": {"title": "Gallery", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-gallery.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-gallery-600x1356.jpg", "astra-page-api-url": "https://websitedemos.net/pool-services-07/wp-json/wp/v2/pages/637", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pool-services-07/gallery/", "astra-sites-tag": {"461": "gallery"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85355": {"title": "Reviews", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-reviews.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-reviews-600x1502.jpg", "astra-page-api-url": "https://websitedemos.net/pool-services-07/wp-json/wp/v2/pages/639", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pool-services-07/reviews/", "astra-sites-tag": {"1615": "reviews"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85352": {"title": "FAQ", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-faq.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-faq-600x1021.jpg", "astra-page-api-url": "https://websitedemos.net/pool-services-07/wp-json/wp/v2/pages/641", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pool-services-07/faq/", "astra-sites-tag": {"426": "faq"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85351": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/pool-services-07-contact-600x1298.jpg", "astra-page-api-url": "https://websitedemos.net/pool-services-07/wp-json/wp/v2/pages/643", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/pool-services-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48300": {"title": "Custom Printing", "id": 48300, "publish-date": 1603905872, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08-600x2002.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08-400x1335.jpg", "astra-site-url": "//websitedemos.net/custom-printing-08", "astra-site-parent-id": 1719, "astra-sites-tag": {"2418": "commerce", "400": "custom-printing", "1017": "e-commerce", "496": "ecommerce", "1817": "ecommerce-store", "2419": "eshopping", "881": "mug-printing", "2422": "online-market", "732": "online-shop", "2420": "online-shopping", "383": "online-store", "967": "product-store", "2421": "purchases", "2417": "reseller", "733": "shop", "2416": "shopping", "749": "store", "405": "t-shirt-printing", "966": "woo-commerce", "1024": "woocommerce"}, "astra-sites-type": "free", "astra-site-category": {"38": "ecommerce", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2495": "ecommerce"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-48303": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08-600x2002.jpg", "astra-page-api-url": "https://websitedemos.net/custom-printing-08/wp-json/wp/v2/pages/2089", "dynamic-page": "yes", "astra-page-url": "//websitedemos.net/custom-printing-08/", "astra-sites-tag": {"814": "homepage"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "variation-swatches-woo", "init": "variation-swatches-woo/variation-swatches-woo.php", "name": "Variation Swatches for WooCommerce"}, {"slug": "woocommerce", "init": "woocommerce/woocommerce.php", "name": "WooCommerce"}, {"slug": "woo-cart-abandonment-recovery", "init": "woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php", "name": "WooCommerce Cart Abandonment Recovery"}, {"slug": "woocommerce-payments", "init": "woocommerce-payments/woocommerce-payments.php", "name": "WooPayments"}]}, "id-48301": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08-about-600x1552.jpg", "astra-page-api-url": "https://websitedemos.net/custom-printing-08/wp-json/wp/v2/pages/2161", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/custom-printing-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48302": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/custom-printing-08-contact-600x554.jpg", "astra-page-api-url": "https://websitedemos.net/custom-printing-08/wp-json/wp/v2/pages/2162", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/custom-printing-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"64": "ecommerce", "61": "free"}, "required-plugins": [{"slug": "astra-widgets", "init": "astra-widgets/astra-widgets.php", "name": "Astra Widgets"}, {"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-55455": {"title": "Professional Services", "id": 55455, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-2.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-2-600x1765.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-2-400x1177.jpg", "astra-site-url": "//websitedemos.net/professional-services-08", "astra-site-parent-id": 2123, "astra-sites-tag": {"2131": "accounting-firm", "2135": "advisory-services", "2134": "advocate", "2133": "advocate-firm", "2132": "auditing-firm", "2136": "business-coaching", "2129": "professional-coaching"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2483": "expert-services"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-55458": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-2.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-2-600x1765.jpg", "astra-page-api-url": "https://websitedemos.net/professional-services-08/wp-json/wp/v2/pages/6", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/professional-services-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-55456": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-about-us-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-about-us-1-600x1490.jpg", "astra-page-api-url": "https://websitedemos.net/professional-services-08/wp-json/wp/v2/pages/7", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/professional-services-08/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-55459": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-services-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-services-1-600x1034.jpg", "astra-page-api-url": "https://websitedemos.net/professional-services-08/wp-json/wp/v2/pages/8", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/professional-services-08/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-55457": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-contact-us-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/professional-services-08-contact-us-1-600x1010.jpg", "astra-page-api-url": "https://websitedemos.net/professional-services-08/wp-json/wp/v2/pages/9", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/professional-services-08/contact-us/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-93995": {"title": "Five Days Website", "id": 93995, "publish-date": 1735313817, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-600x3324.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-400x2216.jpg", "astra-site-url": "//websitedemos.net/creative-agency-07", "astra-site-parent-id": 3648, "astra-sites-tag": {"663": "agency", "664": "digital-services", "665": "service-agency", "661": "website"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2812": "digital-agency", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2487": "agency", "3429": "premium"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "Energetic web template for digital agencies with bright colors and instant appeal", "pages": {"id-93996": {"title": "Case Studies", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-case-studies.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-case-studies-600x1404.jpg", "astra-page-api-url": "https://websitedemos.net/creative-agency-07/wp-json/wp/v2/pages/1247", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/creative-agency-07/case-studies/", "astra-sites-tag": {"780": "case-studies"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94000": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-services-600x1365.jpg", "astra-page-api-url": "https://websitedemos.net/creative-agency-07/wp-json/wp/v2/pages/1245", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/creative-agency-07/services/", "astra-sites-tag": {"2518": "business-services"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-94001": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-testimonials-600x1197.jpg", "astra-page-api-url": "https://websitedemos.net/creative-agency-07/wp-json/wp/v2/pages/1249", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/creative-agency-07/testimonials/", "astra-sites-tag": {"2302": "testimonial"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-93997": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-contact-600x1047.jpg", "astra-page-api-url": "https://websitedemos.net/creative-agency-07/wp-json/wp/v2/pages/1253", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/creative-agency-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-93998": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-600x3324.jpg", "astra-page-api-url": "https://websitedemos.net/creative-agency-07/wp-json/wp/v2/pages/1242", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/creative-agency-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-93999": {"title": "Prices", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-prices.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/creative-agency-07-prices-600x1666.jpg", "astra-page-api-url": "https://websitedemos.net/creative-agency-07/wp-json/wp/v2/pages/1251", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/creative-agency-07/prices/", "astra-sites-tag": {"815": "price"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85456": {"title": "Finance &amp; Consulting", "id": 85456, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-600x2093.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-400x1395.jpg", "astra-site-url": "//websitedemos.net/finance-consulting-07", "astra-site-parent-id": 3425, "astra-sites-tag": {"447": "accountant", "448": "accounting", "581": "advice", "2288": "business-consulting", "449": "chartered-accountant", "1898": "consulting", "446": "finance", "3092": "finance-consultant", "582": "financial-investment", "775": "investment-banking"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2823": "accounting", "2777": "business", "2794": "consulting", "2805": "finance", "2866": "insurance", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2481": "finance-service"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85461": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-600x2093.jpg", "astra-page-api-url": "https://websitedemos.net/finance-consulting-07/wp-json/wp/v2/pages/698", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/finance-consulting-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85457": {"title": "About Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-about-us-600x1646.jpg", "astra-page-api-url": "https://websitedemos.net/finance-consulting-07/wp-json/wp/v2/pages/700", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/finance-consulting-07/about-us/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85462": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-services-600x1451.jpg", "astra-page-api-url": "https://websitedemos.net/finance-consulting-07/wp-json/wp/v2/pages/699", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/finance-consulting-07/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85458": {"title": "Careers", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-careers.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-careers-600x1484.jpg", "astra-page-api-url": "https://websitedemos.net/finance-consulting-07/wp-json/wp/v2/pages/702", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/finance-consulting-07/careers/", "astra-sites-tag": {"3093": "careers"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85459": {"title": "Clients", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-clients.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-clients-600x1758.jpg", "astra-page-api-url": "https://websitedemos.net/finance-consulting-07/wp-json/wp/v2/pages/701", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/finance-consulting-07/clients/", "astra-sites-tag": {"450": "clients"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85460": {"title": "Contact Us", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/finance-consulting-07-contact-600x855.jpg", "astra-page-api-url": "https://websitedemos.net/finance-consulting-07/wp-json/wp/v2/pages/703", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/finance-consulting-07/contact-us/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-22317": {"title": "Photographer", "id": 22317, "publish-date": 1561719372, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-1-600x2538.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-1-400x1692.jpg", "astra-site-url": "//websitedemos.net/photographer-08", "astra-site-parent-id": 75, "astra-sites-tag": {"513": "camera", "482": "fashion", "515": "lens", "510": "modelling", "511": "photographer", "509": "photography", "444": "portfolio", "514": "videography", "512": "wedding-shoot"}, "astra-sites-type": "free", "astra-site-category": {"37": "blog", "39": "free"}, "astra-sites-template-category": {"2770": "personal", "2773": "photography", "2768": "portfolio", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2485": "photography", "2488": "portfolio"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-22320": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-1-600x2538.jpg", "astra-page-api-url": "https://websitedemos.net/photographer-08/wp-json/wp/v2/pages/8", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/photographer-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-22318": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-about-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-about-1-600x1349.jpg", "astra-page-api-url": "https://websitedemos.net/photographer-08/wp-json/wp/v2/pages/26", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/photographer-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-22319": {"title": "Blog", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-blog-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2019/06/photographer-08-blog-1-600x961.jpg", "astra-page-api-url": "https://websitedemos.net/photographer-08/wp-json/wp/v2/pages/267", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/photographer-08/blog/", "astra-sites-tag": {"480": "blog"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"74": "blog", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85430": {"title": "Author", "id": 85430, "publish-date": 1706206827, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-600x1943.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-400x1295.jpg", "astra-site-url": "//websitedemos.net/author-07", "astra-site-parent-id": 3422, "astra-sites-tag": {"724": "author", "726": "literature", "723": "novelist", "1020": "personal", "722": "poet", "1844": "premium", "725": "writer"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2839": "author-writer", "2814": "bookstore", "2769": "ecommerce"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2488": "portfolio"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85434": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-600x1943.jpg", "astra-page-api-url": "https://websitedemos.net/author-07/wp-json/wp/v2/pages/519", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/author-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85431": {"title": "About Me", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-about-me.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-about-me-600x1948.jpg", "astra-page-api-url": "https://websitedemos.net/author-07/wp-json/wp/v2/pages/525", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/author-07/about-me/", "astra-sites-tag": {"1007": "about-me"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85432": {"title": "Books", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-books.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-books-600x1731.jpg", "astra-page-api-url": "https://websitedemos.net/author-07/wp-json/wp/v2/pages/523", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/author-07/books/", "astra-sites-tag": {"1740": "books"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85433": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/author-07-contact-600x865.jpg", "astra-page-api-url": "https://websitedemos.net/author-07/wp-json/wp/v2/pages/527", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/author-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-48391": {"title": "Windows &#038; Doors Services", "id": 48391, "publish-date": 1603911926, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-1-600x2034.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-1-400x1356.jpg", "astra-site-url": "//websitedemos.net/windows-and-doors-08", "astra-site-parent-id": 1730, "astra-sites-tag": {"435": "architecture", "440": "doors", "436": "interior", "1021": "local-business", "439": "local-services", "441": "technician", "438": "windows"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": {"2795": "architecture", "2777": "business", "2768": "portfolio", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2471": "local-technician"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-48395": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-1-600x2034.jpg", "astra-page-api-url": "https://websitedemos.net/windows-and-doors-08/wp-json/wp/v2/pages/10", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/windows-and-doors-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48392": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-about-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-about-1-600x1247.jpg", "astra-page-api-url": "https://websitedemos.net/windows-and-doors-08/wp-json/wp/v2/pages/13", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/windows-and-doors-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48397": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-services-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-services-1-600x1696.jpg", "astra-page-api-url": "https://websitedemos.net/windows-and-doors-08/wp-json/wp/v2/pages/15", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/windows-and-doors-08/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48396": {"title": "Portfolio", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-portfolio-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-portfolio-1-600x1310.jpg", "astra-page-api-url": "https://websitedemos.net/windows-and-doors-08/wp-json/wp/v2/pages/12", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/windows-and-doors-08/portfolio/", "astra-sites-tag": {"444": "portfolio"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48394": {"title": "FAQs", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-faqs-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-faqs-1-600x810.jpg", "astra-page-api-url": "https://websitedemos.net/windows-and-doors-08/wp-json/wp/v2/pages/171", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/windows-and-doors-08/faqs/", "astra-sites-tag": {"426": "faq"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-48393": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-contact-1.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2020/10/windows-and-doors-08-contact-1-600x792.jpg", "astra-page-api-url": "https://websitedemos.net/windows-and-doors-08/wp-json/wp/v2/pages/180", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/windows-and-doors-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-93921": {"title": "Accountant", "id": 93921, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-600x1728.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-400x1152.jpg", "astra-site-url": "//websitedemos.net/chartered-accountant-08", "astra-site-parent-id": 3646, "astra-sites-tag": {"447": "accountant", "448": "accounting", "449": "chartered-accountant", "446": "finance", "1022": "professional"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": {"2823": "accounting", "2777": "business", "2805": "finance", "2866": "insurance", "2770": "personal", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2481": "finance-service", "3428": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "Interesting design that’s perfect for accountants and all kinds of service businesses", "pages": {"id-93922": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-about.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-about-600x1043.jpg", "astra-page-api-url": "https://websitedemos.net/chartered-accountant-08/wp-json/wp/v2/pages/387", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/chartered-accountant-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-93923": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-contact-600x874.jpg", "astra-page-api-url": "https://websitedemos.net/chartered-accountant-08/wp-json/wp/v2/pages/395", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/chartered-accountant-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}, "id-93924": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-600x1728.jpg", "astra-page-api-url": "https://websitedemos.net/chartered-accountant-08/wp-json/wp/v2/pages/384", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/chartered-accountant-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-93925": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-services.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-services-600x1280.jpg", "astra-page-api-url": "https://websitedemos.net/chartered-accountant-08/wp-json/wp/v2/pages/389", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/chartered-accountant-08/services/", "astra-sites-tag": {"2518": "business-services"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-93926": {"title": "Testimonials", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-testimonials.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-testimonials-600x1080.jpg", "astra-page-api-url": "https://websitedemos.net/chartered-accountant-08/wp-json/wp/v2/pages/393", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/chartered-accountant-08/testimonials/", "astra-sites-tag": {"433": "testimonials"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-93927": {"title": "Why choose me", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-why-choose-me.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/12/chartered-accountant-08-why-choose-me-600x819.jpg", "astra-page-api-url": "https://websitedemos.net/chartered-accountant-08/wp-json/wp/v2/pages/391", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/chartered-accountant-08/why-choose-me/", "astra-sites-tag": {"760": "why-choose-us"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-85331": {"title": "Law Firm", "id": 85331, "publish-date": **********, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-600x2654.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-400x1769.jpg", "astra-site-url": "//websitedemos.net/law-firm-07", "astra-site-parent-id": 3409, "astra-sites-tag": {"704": "corporate-law", "701": "criminal-law", "703": "enforcement", "702": "law-firm", "998": "law-office", "706": "lawyer", "778": "legal", "917": "legal-firm"}, "astra-sites-type": "agency-mini", "astra-site-category": {"27": "business"}, "astra-sites-template-category": {"2777": "business", "2824": "law-firm", "2831": "service"}, "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2482": "law"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-85335": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-600x2654.jpg", "astra-page-api-url": "https://websitedemos.net/law-firm-07/wp-json/wp/v2/pages/634", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/law-firm-07/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85332": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-about-us.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-about-us-600x1408.jpg", "astra-page-api-url": "https://websitedemos.net/law-firm-07/wp-json/wp/v2/pages/637", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/law-firm-07/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85333": {"title": "Attorneys", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-attorneys.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-attorneys-600x1121.jpg", "astra-page-api-url": "https://websitedemos.net/law-firm-07/wp-json/wp/v2/pages/641", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/law-firm-07/attorneys/", "astra-sites-tag": {"912": "attorneys"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85336": {"title": "Practice Areas", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-practice-areas.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-practice-areas-600x1403.jpg", "astra-page-api-url": "https://websitedemos.net/law-firm-07/wp-json/wp/v2/pages/639", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/law-firm-07/practice-areas/", "astra-sites-tag": {"707": "practice-area"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-85334": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-contact.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2024/01/law-firm-07-contact-600x1024.jpg", "astra-page-api-url": "https://websitedemos.net/law-firm-07/wp-json/wp/v2/pages/643", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/law-firm-07/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "agency-mini", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}, "id-55398": {"title": "Salon &amp; Spa", "id": 55398, "publish-date": 1621884270, "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-08-600x2474.jpg", "fullpage-thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-08-400x1649.jpg", "astra-site-url": "//websitedemos.net/stylist-08", "astra-site-parent-id": 2101, "astra-sites-tag": {"2102": "beautician", "2121": "beauty-parlor", "2106": "beauty-salon", "2120": "fashion-and-beauty", "1642": "hair-salon", "2108": "hairdresser", "577": "makeup-artist", "2119": "salon-and-spa", "530": "spa", "2107": "stylist", "619": "wellness"}, "astra-sites-type": "free", "astra-site-category": {"27": "business", "39": "free"}, "astra-sites-template-category": [], "astra-site-page-builder": "<PERSON><PERSON>", "categories": {"2479": "beauty"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}], "post-status": "publish", "post-content": "", "post-excerpt": "", "pages": {"id-55401": {"title": "Home", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-08-600x2474.jpg", "astra-page-api-url": "https://websitedemos.net/stylist-08/wp-json/wp/v2/pages/6", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/stylist-08/", "astra-sites-tag": {"423": "home"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-55399": {"title": "About", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-about-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-about-08-600x1247.jpg", "astra-page-api-url": "https://websitedemos.net/stylist-08/wp-json/wp/v2/pages/900", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/stylist-08/about/", "astra-sites-tag": {"352": "about"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-55402": {"title": "Services", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-services-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-services-08-600x1642.jpg", "astra-page-api-url": "https://websitedemos.net/stylist-08/wp-json/wp/v2/pages/902", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/stylist-08/services/", "astra-sites-tag": {"425": "services"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}]}, "id-55400": {"title": "Contact", "featured-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-contact-08.jpg", "thumbnail-image-url": "https://websitedemos.net/wp-content/uploads/2021/05/stylist-contact-08-600x879.jpg", "astra-page-api-url": "https://websitedemos.net/stylist-08/wp-json/wp/v2/pages/904", "dynamic-page": "no", "astra-page-url": "//websitedemos.net/stylist-08/contact/", "astra-sites-tag": {"415": "contact"}, "site-pages-type": "free", "site-pages-page-builder": "<PERSON><PERSON>", "site-pages-category": [], "site-pages-parent-category": {"60": "business", "61": "free"}, "required-plugins": [{"slug": "ultimate-addons-for-gutenberg", "init": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "name": "Spectra"}, {"slug": "sureforms", "init": "sureforms/sureforms.php", "name": "SureForms"}]}}, "related-bb-template": "", "related-elementor-template": "", "related-gutenberg-template": "", "related_ecommerce_template": "", "ecommerce_parent_template": ""}}